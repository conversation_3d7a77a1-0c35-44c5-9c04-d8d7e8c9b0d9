
variables:
  NODE_VERSION: "22"

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      variables:
        runBuild: true
        PUBLISH_COVERAGE: "true"
        # Use a path prefix for MR's so multiple coverage reports can be published at once
        COVERAGE_PATH_PREFIX: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - if: $CI_COMMIT_BRANCH == "main"
      variables:
        PUBLISH_COVERAGE: "true"
    - if: $CI_COMMIT_BRANCH =~ /user/
      variables:
        runBuild: true
    - if: $CI_PIPELINE_SOURCE == 'web'
      variables:
        runBuild: false

stages:
  - test
  - build
  - deploy

unit-tests:
  stage: test
  image: "node:${NODE_VERSION}"
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
  script:
    - npm install
    - npm run test:coverage
    # correct value of <source> element in cobertura report to allow Gitlab to recognise coverage
    - sed -i "s#<source>/builds/coopeng/retail/stores/store-hub/store-hub-app</source>#<source></source>#g" coverage/cobertura-coverage.xml
  coverage: /Statements\s+:\s+([\d\.]+)/
  artifacts:
    when: always
    paths:
      - test-results/**/*.xml
      - coverage/
    reports:
      junit: test-results/**/*.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    expire_in: 1 week

e2e-tests:
  stage: test
  image: mcr.microsoft.com/playwright:v1.52.0-noble
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
  script:
    - npm install
    - npm run setup-e2e
    - npm run test:e2e
  artifacts:
    when: always
    reports:
      junit: test-results/playwright/results.xml
    paths:
      - playwright-report/
      - test-results/
    expire_in: 1 week

build-job:
  stage: build
  image: "node:${NODE_VERSION}"
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
  rules:
    - if: $runBuild
  script:
    - npm install
    - npm run build

publish-coverage:
  stage: deploy
  dependencies:
    - unit-tests
  rules:
    - if: $PUBLISH_COVERAGE
  script:
    - mv coverage/ public/
  pages:
    path_prefix: $COVERAGE_PATH_PREFIX
    expire_in: 14 days