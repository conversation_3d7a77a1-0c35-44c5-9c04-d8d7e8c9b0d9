# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/test-results

# jest cache and artifacts
/.jest-cache
/.jest-cache/
**/jest-cache/
**/.jest-cache/
# Jest haste maps and transform cache
**/haste-map-*
**/perf-cache-*
**/*test*.jest-cache*
**/*.jest-transform-cache*
# Source maps (unless specifically needed for debugging)
**/*.map

# playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode
.idea
.DS_Store