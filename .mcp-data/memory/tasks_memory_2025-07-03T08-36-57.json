{"tasks": [{"id": "fe238254-fa5f-4fbd-9c5b-19a0f5e998e5", "name": "Create HOW_DOI_SEARCH_ENABLED project-level CI/CD variable", "description": "Create the HOW_DOI_SEARCH_ENABLED environment variable as a project-level CI/CD variable in GitLab UI instead of hardcoding it in .gitlab-ci.yml. This follows <PERSON>'s engineering guidance and GitLab best practices for centralized variable management.", "notes": "This is the recommended approach following <PERSON>'s guidance. Project-level CI/CD variables are automatically available to all pipeline jobs and can be managed centrally without code changes. The variable will be accessible during build, test, and deployment stages.", "status": "completed", "dependencies": [], "createdAt": "2025-07-03T06:48:56.177Z", "updatedAt": "2025-07-03T06:55:04.129Z", "relatedFiles": [{"path": "src/app/actions/config.ts", "type": "REFERENCE", "description": "Server action that reads the environment variable during build compilation", "lineStart": 7, "lineEnd": 11}], "implementationGuide": "1. Navigate to your GitLab project\\n2. Go to Settings > CI/CD\\n3. Expand the Variables section\\n4. Click Add Variable\\n5. Configure the variable:\\n   - Key: HOW_DOI_SEARCH_ENABLED\\n   - Value: true\\n   - Type: Variable (default)\\n   - Environment scope: * (All environments)\\n   - Protect variable: Unchecked (unless you want it only on protected branches)\\n   - Visibility: Visible (default)\\n6. Click Add Variable to save\\n\\nThis variable will now be automatically available to ALL jobs in your pipeline without needing to hardcode it in .gitlab-ci.yml", "verificationCriteria": "1. Variable is created in GitLab project Settings > CI/CD > Variables\\n2. Variable key is HOW_DOI_SEARCH_ENABLED with value true\\n3. Environment scope is set to * (all environments)\\n4. Variable is visible in pipeline job logs (unless masked)\\n5. No hardcoded values remain in .gitlab-ci.yml", "analysisResult": "Fix GitLab CI/CD pipeline issue where HOW_DOI_SEARCH_ENABLED environment variable is not available during the build stage by implementing GitLab project-level CI/CD Variables instead of hardcoding values in .gitlab-ci.yml, following <PERSON>'s engineering guidance to avoid hardcoded values and use GitLab's centralized variable management.", "summary": "Successfully created HOW_DOI_SEARCH_ENABLED project-level CI/CD variable in GitLab UI following <PERSON>'s engineering guidance to avoid hardcoded values. Variable is configured with key HOW_DOI_SEARCH_ENABLED, value true, environment scope * (all environments), and will be automatically available to all pipeline jobs including the critical build-job stage where Next.js compiles server actions.", "completedAt": "2025-07-03T06:55:04.118Z"}, {"id": "1fae6527-5ba2-4224-a624-eb2f5ec252ad", "name": "Remove hardcoded NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED from .gitlab-ci.yml", "description": "Remove the hardcoded NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED variable from the .gitlab-ci.yml file since we're now using the server-side HOW_DOI_SEARCH_ENABLED project variable. This cleans up the configuration and follows the team's decision to use server-side feature flags.", "notes": "This cleanup removes the old client-side variable that was replaced with the server-side implementation. The project-level CI/CD variable will now handle the feature flag for all environments.", "status": "completed", "dependencies": [{"taskId": "fe238254-fa5f-4fbd-9c5b-19a0f5e998e5"}], "createdAt": "2025-07-03T06:48:56.177Z", "updatedAt": "2025-07-03T06:55:50.357Z", "relatedFiles": [{"path": ".gitlab-ci.yml", "type": "TO_MODIFY", "description": "GitLab CI configuration file where hardcoded variable needs to be removed", "lineStart": 65, "lineEnd": 65}], "implementationGuide": "1. Open .gitlab-ci.yml file\\n2. Locate line 65 with: NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED: \\\"true\\\"\\n3. Remove this line completely\\n4. Ensure proper YAML formatting is maintained\\n5. Verify no other hardcoded HOW_DOI_SEARCH_ENABLED references exist\\n\\nThe file should look like:\\n```yaml\\ne2e-tests:\\n  stage: test\\n  image: \\\"mcr.microsoft.com/playwright:v1.48.0-noble\\\"\\n  variables:\\n    # NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED line removed\\n  cache:\\n    key:\\n      files:\\n        - package-lock.json\\n```", "verificationCriteria": "1. NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED line is removed from .gitlab-ci.yml\\n2. YAML file remains valid and properly formatted\\n3. No other hardcoded HOW_DOI_SEARCH_ENABLED references exist\\n4. Pipeline continues to run successfully\\n5. e2e-tests job still functions correctly", "analysisResult": "Fix GitLab CI/CD pipeline issue where HOW_DOI_SEARCH_ENABLED environment variable is not available during the build stage by implementing GitLab project-level CI/CD Variables instead of hardcoding values in .gitlab-ci.yml, following <PERSON>'s engineering guidance to avoid hardcoded values and use GitLab's centralized variable management.", "summary": "Successfully removed hardcoded HOW_DOI_SEARCH_ENABLED variable from line 58 in .gitlab-ci.yml e2e-tests job. The variables section has been completely removed since it only contained the hardcoded value. YAML formatting remains valid and clean. No other hardcoded HOW_DOI_SEARCH_ENABLED references exist in the file. The project-level CI/CD variable will now handle the feature flag for all pipeline jobs including build-job.", "completedAt": "2025-07-03T06:55:50.345Z"}]}