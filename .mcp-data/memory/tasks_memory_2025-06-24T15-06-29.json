{"tasks": [{"id": "eee73323-689d-42cb-b954-2ece6c99822f", "name": "Simplify HowDoISearchBar component to human-written style", "description": "Refactor the HowDoISearchBar component to remove AI-generated patterns and match the project's simple, direct style. Remove optional callback functionality, simplify error handling, reduce loading state complexity, clean up verbose comments, and reduce the component from 135 lines to approximately 60 lines following patterns from AppItem.tsx and BottomNavBar.tsx.", "notes": "The current implementation has AI-generated patterns that don't match the project's human-written style. Other components like AppItem.tsx (27 lines) and BottomNavBar.tsx (27 lines) are simple and direct without optional callbacks or complex error handling.", "status": "completed", "dependencies": [], "createdAt": "2025-06-24T14:56:40.929Z", "updatedAt": "2025-06-24T14:57:43.628Z", "relatedFiles": [{"path": "src/components/HowDoISearchBar.tsx", "type": "TO_MODIFY", "description": "Main component to be simplified", "lineStart": 1, "lineEnd": 135}, {"path": "src/components/pages/apps/AppItem.tsx", "type": "REFERENCE", "description": "Example of project's simple, direct component style", "lineStart": 1, "lineEnd": 27}, {"path": "src/components/layout/navigation/BottomNavBar.tsx", "type": "REFERENCE", "description": "Example of project's minimal component pattern", "lineStart": 1, "lineEnd": 27}], "implementationGuide": "1. Remove optional callback (onLaunch) from interface and all usage\\n2. Remove try/catch error handling - call launchHowDoIApp directly\\n3. Simplify loading state to just isSubmitting boolean\\n4. Remove verbose comments like 'Call optional callback' and 'Clear the input after submission'\\n5. Simplify tailwind-variants configuration - remove complex loading state variants\\n6. Keep only essential accessibility attributes that match project patterns\\n7. Ensure component follows project's minimal, direct style like other components\\n8. Reduce component to ~60 lines total", "verificationCriteria": "Component reduced to ~60 lines, optional callback removed, error handling simplified, loading state simplified to boolean, verbose comments removed, tailwind-variants simplified, code appears human-written and matches project patterns", "analysisResult": "Clean up HowDoISearchBar component and related code changes to make them look human-written rather than AI-generated. Remove redundant code like optional callbacks, simplify overly complex comments and error handling, and ensure the code style appears natural and minimal following the project's established patterns of simplicity and directness.", "summary": "Successfully simplified HowDoISearchBar component from 135 lines to 78 lines, removing AI-generated patterns and making it look human-written. Removed optional callback functionality, simplified error handling to direct function calls, reduced loading state complexity to simple boolean, removed verbose comments, and simplified tailwind-variants configuration. The component now follows project patterns like AppItem.tsx and BottomNavBar.tsx with clean, minimal, and direct implementation style.", "completedAt": "2025-06-24T14:57:43.621Z"}]}