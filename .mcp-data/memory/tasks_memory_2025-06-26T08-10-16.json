{"tasks": [{"id": "f1dd6b92-a379-414d-ac5c-1ad23f9c6e5d", "name": "Remove MSW Server Setup and Dependencies", "description": "Remove Mock Service Worker (MSW) server setup from test configuration which is causing performance overhead and complexity. This includes removing MSW imports, server lifecycle management, and related configuration that adds 578ms setup time and causes unhandled request warnings.", "status": "completed", "dependencies": [], "createdAt": "2025-06-26T07:48:28.371Z", "updatedAt": "2025-06-26T07:49:06.965Z", "relatedFiles": [{"path": "src/test/setup.ts", "type": "TO_MODIFY", "description": "Main test setup file containing MSW server configuration", "lineStart": 1, "lineEnd": 100}, {"path": "package.json", "type": "REFERENCE", "description": "Check MSW dependency usage", "lineStart": 1, "lineEnd": 50}], "implementationGuide": "1. Edit src/test/setup.ts to remove MSW imports and server setup\\n2. Remove setupServer, beforeAll/afterAll MSW lifecycle\\n3. Keep essential global mocks (IntersectionObserver, ResizeObserver, matchMedia, localStorage)\\n4. Remove console suppression logic that adds overhead\\n5. Simplify the file to focus only on necessary browser API mocks", "verificationCriteria": "MSW server setup removed, test setup file simplified, no MSW imports remaining, essential browser mocks preserved", "analysisResult": "Simplify and clean up test suite to resolve flaky behavior and long wait times. Remove MSW server setup, simplify test configuration, and streamline to basic functionality tests that run quickly and reliably. Current tests take 26.59 seconds with MSW overhead - target is 5-8 seconds with simple mocks.", "summary": "Successfully removed MSW server setup from src/test/setup.ts. Eliminated MSW imports (setupServer, http, HttpResponse), removed server lifecycle management (beforeAll/afterAll with server.listen/close), removed console suppression logic that added overhead, and kept only essential browser API mocks (IntersectionObserver, ResizeObserver, matchMedia, localStorage). The file is now simplified and focused on necessary mocks only.", "completedAt": "2025-06-26T07:49:06.957Z"}, {"id": "1c7ae823-8f0f-4066-a7ac-d6aa16b1f249", "name": "Remove MSW Mock Handlers and Complex Mock Data", "description": "Delete MSW handlers file and simplify mock data to basic objects needed for testing. The current handlers.ts file contains complex API simulation with timeouts and scenarios that are unnecessary for unit tests and contribute to test complexity.", "status": "completed", "dependencies": [{"taskId": "f1dd6b92-a379-414d-ac5c-1ad23f9c6e5d"}], "createdAt": "2025-06-26T07:48:28.371Z", "updatedAt": "2025-06-26T07:50:31.852Z", "relatedFiles": [{"path": "src/test/mocks/handlers.ts", "type": "TO_MODIFY", "description": "MSW handlers file to be deleted", "lineStart": 1, "lineEnd": 200}, {"path": "src/test/mocks/billsData.ts", "type": "TO_MODIFY", "description": "Mock data file to be simplified", "lineStart": 1, "lineEnd": 200}], "implementationGuide": "1. Delete src/test/mocks/handlers.ts entirely\\n2. Simplify src/test/mocks/billsData.ts to contain only basic mock objects\\n3. Remove complex API response simulation functions\\n4. Keep only essential mock bill objects and types\\n5. Remove scenario handlers, error handlers, and timeout simulations", "verificationCriteria": "handlers.ts file deleted, billsData.ts simplified to basic mock objects, no complex API simulation remaining", "analysisResult": "Simplify and clean up test suite to resolve flaky behavior and long wait times. Remove MSW server setup, simplify test configuration, and streamline to basic functionality tests that run quickly and reliably. Current tests take 26.59 seconds with MSW overhead - target is 5-8 seconds with simple mocks.", "summary": "Successfully removed MSW handlers file and simplified mock data. Deleted src/test/mocks/handlers.ts entirely (contained 150+ lines of complex API simulation with timeouts, error scenarios, and pagination logic). Simplified src/test/mocks/billsData.ts from 200+ lines to 60 lines, removing complex functions like createPaginatedMockResponse, createFilteredMockResponse, error handlers, and API response simulation. Kept only essential mock bill objects and basic bill types needed for testing. All complex API simulation logic has been eliminated.", "completedAt": "2025-06-26T07:50:31.841Z"}]}