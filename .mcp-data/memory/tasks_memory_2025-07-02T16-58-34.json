{"tasks": [{"id": "15cb61cf-ccbf-48f2-b4db-2ea54714beac", "name": "Add environment variable to GitLab CI e2e-tests job", "description": "Add NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED environment variable to the e2e-tests job in .gitlab-ci.yml to ensure the HowDoISearchBar component is visible during Playwright tests. The variable should be set to 'true' to enable the feature.", "notes": "This is a critical fix - the component checks process.env.NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED !== 'false' and hides itself if the variable is not set. Next.js embeds NEXT_PUBLIC_ variables at build time, so this must be set at the job level.", "status": "completed", "dependencies": [], "createdAt": "2025-07-02T16:50:39.987Z", "updatedAt": "2025-07-02T16:51:06.913Z", "relatedFiles": [{"path": ".gitlab-ci.yml", "type": "TO_MODIFY", "description": "GitLab CI configuration file where the environment variable needs to be added", "lineStart": 55, "lineEnd": 76}, {"path": "src/components/HowDoISearchBar.tsx", "type": "REFERENCE", "description": "Component that checks the environment variable and hides itself if not set", "lineStart": 55, "lineEnd": 57}], "implementationGuide": "1. Open .gitlab-ci.yml file\\n2. Locate the e2e-tests job (around line 55)\\n3. Add a variables section with NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED: 'true'\\n4. Ensure proper YAML indentation\\n\\nExample:\\n```yaml\\ne2e-tests:\\n  stage: test\\n  image: mcr.microsoft.com/playwright:v1.52.0-noble\\n  variables:\\n    NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED: 'true'\\n  cache:\\n    # existing cache config\\n```", "verificationCriteria": "1. Environment variable NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED is set to 'true' in e2e-tests job\\n2. YAML syntax is valid and properly indented\\n3. Variable is placed in the correct job scope\\n4. No other job configurations are affected\\n5. Pipeline should pass when this change is committed", "analysisResult": "Fix GitLab CI pipeline failure for Playwright e2e tests by adding the missing NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED environment variable to the e2e-tests job configuration. The component HowDoISearchBar is hidden when this variable is not set, causing tests to fail when trying to find search input elements.", "summary": "Successfully added NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED environment variable to the e2e-tests job in .gitlab-ci.yml. The variable is set to \"true\" with proper YAML indentation and placement. This fix ensures the HowDoISearchBar component will be visible during Playwright tests, resolving the pipeline failure. The implementation follows existing GitLab CI patterns and maintains proper job structure.", "completedAt": "2025-07-02T16:51:06.907Z"}]}