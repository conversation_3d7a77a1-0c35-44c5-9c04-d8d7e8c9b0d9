# store-hub-app

[![coverage](https://gitlab.com/coopeng/retail/stores/store-hub/store-hub-app/badges/main/coverage.svg)](https://coopeng.gitlab.io/retail/stores/store-hub/store-hub-app/coverage/)

This repo contains the code for the Store Hub app. This is a Next.js app that handles the user interface of Store Hub.

## Getting Started

To initialise your local environment, clone this repo and run:

```bash
npm install
npm run prepare
```

To run the app in development mode run:

```bash
npm run dev
```

The app will be accessible at [http://localhost:3000](http://localhost:3000).

## Testing and CI/CD

### Testing Framework

- **Unit Tests**: Jest with jsdom environment for component and utility testing
- **E2E Tests**: Playwright for end-to-end testing across multiple browsers (Chromium, Firefox, WebKit)

### Setup

Before running E2E tests Playwright must be installed by running:
```bash
npm run setup:e2e
```

### Available Test Scripts
```bash
# Run Jest unit tests
npm test

# Run Playwright E2E tests
npm run test:e2e

# Run E2E tests in headed mode (with browser UI)
npm run test:e2e:headed

# Run E2E tests in debug mode
npm run test:e2e:debug

# View Playwright test report
npm run test:e2e:report

# Open Playwright UI mode
npm run test:e2e:ui
```

### Testing Metrics

Test results and code coverage are integrated with the Gitlab merge request feature. A full code coverage report can be viewed using the link in the default merge request description template.
