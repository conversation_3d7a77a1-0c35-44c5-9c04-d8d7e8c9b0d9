import { test, expect } from '@playwright/test';

test.describe('Main Navigation', () => {
  test('should allow normal navigation between tabs', async ({ page }) => {
    await page.goto('/');

    // Start at home, go to apps
    await page.click('[aria-label="Maps to Apps"]');
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(page.locator('main').getByRole('heading', { name: 'Apps' })).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Apps"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
    await expect(
      page.locator('[aria-label="Maps to Home"]'),
    ).not.toHaveAttribute('aria-current', 'page');

    // Go back to home
    await page.click('[aria-label="Maps to Home"]');
    await expect(page).toHaveURL('/');
    await expect(page.locator('main').getByText('Home')).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Home"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
    await expect(
      page.locator('[aria-label="Maps to Apps"]'),
    ).not.toHaveAttribute('aria-current', 'page');
  });

  test('should stay on /apps page when refreshed', async ({ page }) => {
    // Go to /apps first by clicking
    await page.goto('/');
    await page.click('[aria-label="Maps to Apps"]');
    await expect(page).toHaveURL(/\/apps\/?/);

    // Reload the page
    await page.reload();

    // It should stay on apps page
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(page.locator('main').getByRole('heading', { name: 'Apps' })).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Apps"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
    await expect(
      page.locator('[aria-label="Maps to Home"]'),
    ).not.toHaveAttribute('aria-current', 'page');
  });

  test('direct navigation to /apps should work correctly', async ({
    page,
  }) => {
    // Go directly to /apps
    await page.goto('/apps');

    // It should stay on apps page and select the correct tab
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(page.locator('main').getByRole('heading', { name: 'Apps' })).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Apps"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
  });
});
