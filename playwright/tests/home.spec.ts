import { test, expect } from '@playwright/test';
import { viewports } from '../../src/config/breakpoints';

test.describe('Home Page', () => {
  test('should load and display main elements', async ({ page }) => {
    await page.goto('/');

    const searchInput = page.getByRole('textbox', {
      name: 'Ask How Do I question',
    });
    await expect(searchInput).toBeVisible();
  });

  test.describe('How Do I Search Bar', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/');
    });

    test('should display and function correctly', async ({ page }) => {
      const searchInput = page.getByRole('textbox', {
        name: 'Ask How Do I question',
      });
      const searchButton = page.getByRole('button', { name: 'Search' });

      await expect(searchInput).toBeVisible();
      await expect(searchButton).toBeVisible();
      await expect(searchInput).toHaveAttribute(
        'placeholder',
        'Ask How Do I...',
      );

      await expect(searchButton).toBeDisabled();

      await searchInput.fill('how do I reset my password');
      await expect(searchButton).toBeEnabled();

      await searchInput.clear();
      await expect(searchButton).toBeDisabled();

      await searchInput.fill('   ');
      await expect(searchButton).toBeDisabled();
    });

    test('should work on mobile and tablet', async ({ page }) => {
      const searchInput = page.getByRole('textbox', {
        name: 'Ask How Do I question',
      });
      const searchButton = page.getByRole('button', { name: 'Search' });

      await page.setViewportSize(viewports.mobile);
      await expect(searchInput).toBeVisible();
      await expect(searchButton).toBeVisible();

      await page.setViewportSize(viewports.tablet);
      await expect(searchInput).toBeVisible();
      await expect(searchButton).toBeVisible();
    });
  });
});
