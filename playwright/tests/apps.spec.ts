import { test, expect } from '@playwright/test';

test.describe('Apps Page', () => {
  test('should load and display apps', async ({ page }) => {
    await page.goto('/apps');

    await expect(page.getByRole('heading', { name: 'Apps' })).toBeInViewport();

    const appItemsCount = await page
      .locator('[data-testid^="app-item-"]')
      .count();
    expect(appItemsCount).toBeGreaterThan(0);
  });

  test.describe('Category Filtering', () => {
    test('should display all categories and default to "All apps"', async ({
      page,
    }) => {
      await page.goto('/apps');

      await expect(
        page.getByRole('tablist', { name: 'Filter apps by category' }),
      ).toBeInViewport();

      const expectedCategories = [
        'All apps',
        'Analytics',
        'Customer service',
        'Operations',
        'Stock management',
        'Workforce',
      ];

      for (const category of expectedCategories) {
        await expect(
          page.getByRole('tab', { name: category }),
        ).toBeInViewport();
      }

      await expect(page.getByRole('tab', { name: 'All apps' })).toHaveAttribute(
        'aria-selected',
        'true',
      );
    });

    test('should filter apps by Stock management category', async ({
      page,
    }) => {
      await page.goto('/apps');

      await page.getByRole('tab', { name: 'Stock management' }).click();

      await expect(
        page.getByRole('tab', { name: 'Stock management' }),
      ).toHaveAttribute('aria-selected', 'true');

      await expect(page.getByText('ASTs')).toBeInViewport();
      await expect(page.getByText('Amazon app')).toBeInViewport();
      await expect(page.getByText('Inventory Manager')).toBeInViewport();
      await expect(page.getByText('Warehouse Scanner')).toBeInViewport();
    });

    test('should filter apps by Workforce category', async ({ page }) => {
      await page.goto('/apps');

      await page.getByRole('tab', { name: 'Workforce' }).click();

      await expect(
        page.getByRole('tab', { name: 'Workforce' }),
      ).toHaveAttribute('aria-selected', 'true');

      await expect(page.getByText('Bakery app')).toBeInViewport();
      await expect(page.getByText('BI app')).toBeInViewport();
      await expect(page.getByText('Staff Scheduler')).toBeInViewport();
      await expect(page.getByText('Time Tracker')).toBeInViewport();
      await expect(page.getByText('Training Portal')).toBeInViewport();

      await expect(page.getByText('Inventory Manager')).not.toBeVisible();
      await expect(page.getByText('Warehouse Scanner')).not.toBeVisible();
    });

    test('should return to all apps when clicking "All apps" category', async ({
      page,
    }) => {
      await page.goto('/apps');

      await page.getByRole('tab', { name: 'Stock management' }).click();
      await expect(
        page.getByRole('tab', { name: 'Stock management' }),
      ).toHaveAttribute('aria-selected', 'true');
      await expect(page.getByText('Staff Scheduler')).not.toBeVisible();

      await page.getByRole('tab', { name: 'All apps' }).click();

      await expect(page.getByRole('tab', { name: 'All apps' })).toHaveAttribute(
        'aria-selected',
        'true',
      );

      const staffSchedulerApp = page.getByText('Staff Scheduler');
      await staffSchedulerApp.scrollIntoViewIfNeeded();
      await expect(staffSchedulerApp).toBeInViewport();
    });
  });

  test.describe('Responsive Design', () => {
    test('should work correctly on mobile viewport (360px)', async ({
      page,
    }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 360, height: 640 });
      await page.goto('/apps');

      await expect(
        page.getByRole('tablist', { name: 'Filter apps by category' }),
      ).toBeInViewport();

      await page.getByRole('tab', { name: 'Stock management' }).click();

      await expect(page.getByText('Inventory Manager')).toBeInViewport();
    });

    test('should work correctly on tablet viewport (768px)', async ({
      page,
    }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.goto('/apps');

      await expect(
        page.getByRole('tablist', { name: 'Filter apps by category' }),
      ).toBeInViewport();

      await page.getByRole('tab', { name: 'Workforce' }).click();

      await expect(page.getByText('Staff Scheduler')).toBeInViewport();
    });
  });
});
