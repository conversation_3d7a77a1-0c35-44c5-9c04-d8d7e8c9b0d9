import { test, expect } from '@playwright/test';

test.describe('Apps Page', () => {
  test('should load and display apps', async ({ page }) => {
    await page.goto('/apps');

    await expect(page.getByRole('heading', { name: 'Apps' })).toBeVisible();

    const appItemsCount = await page
      .locator('[data-testid^="app-item-"]')
      .count();
    expect(appItemsCount).toBeGreaterThan(0);
  });

  test.describe('Category Filtering', () => {
    test('should display all categories and default to "All apps"', async ({
      page,
    }) => {
      await page.goto('/apps');

      // Check that category filter is visible with all categories
      await expect(
        page.getByRole('tablist', { name: 'Filter apps by category' }),
      ).toBeVisible();

      // Verify all category tabs are present
      const expectedCategories = [
        'All apps',
        'Analytics',
        'Customer service',
        'Operations',
        'Stock management',
        'Workforce',
      ];

      for (const category of expectedCategories) {
        await expect(page.getByRole('tab', { name: category })).toBeVisible();
      }

      // "All apps" should be selected by default
      await expect(page.getByRole('tab', { name: 'All apps' })).toHaveAttribute(
        'aria-selected',
        'true',
      );
    });

    test('should filter apps by Stock management category', async ({
      page,
    }) => {
      await page.goto('/apps');

      // Click on Stock management category
      await page.getByRole('tab', { name: 'Stock management' }).click();

      // Verify category selection
      await expect(
        page.getByRole('tab', { name: 'Stock management' }),
      ).toHaveAttribute('aria-selected', 'true');

      // Verify that only stock management apps are visible
      await expect(page.getByText('ASTs')).toBeVisible();
      await expect(page.getByText('Amazon app')).toBeVisible();
      await expect(page.getByText('Inventory Manager')).toBeVisible();
      await expect(page.getByText('Warehouse Scanner')).toBeVisible();

      // Verify that workforce apps are not visible
      await expect(page.getByText('Staff Scheduler')).not.toBeVisible();
      await expect(page.getByText('Time Tracker')).not.toBeVisible();
    });

    test('should filter apps by Workforce category', async ({ page }) => {
      await page.goto('/apps');

      // Click on Workforce category
      await page.getByRole('tab', { name: 'Workforce' }).click();

      // Verify category selection
      await expect(
        page.getByRole('tab', { name: 'Workforce' }),
      ).toHaveAttribute('aria-selected', 'true');

      // Verify that only workforce apps are visible
      await expect(page.getByText('Bakery app')).toBeVisible();
      await expect(page.getByText('BI app')).toBeVisible();
      await expect(page.getByText('Staff Scheduler')).toBeVisible();
      await expect(page.getByText('Time Tracker')).toBeVisible();
      await expect(page.getByText('Training Portal')).toBeVisible();

      // Verify that stock management apps are not visible
      await expect(page.getByText('Inventory Manager')).not.toBeVisible();
      await expect(page.getByText('Warehouse Scanner')).not.toBeVisible();
    });

    test('should return to all apps when clicking "All apps" category', async ({
      page,
    }) => {
      await page.goto('/apps');

      // First filter by Stock management
      await page.getByRole('tab', { name: 'Stock management' }).click();
      await expect(
        page.getByRole('tab', { name: 'Stock management' }),
      ).toHaveAttribute('aria-selected', 'true');

      // Then click "All apps" to show all apps again
      await page.getByRole('tab', { name: 'All apps' }).click();

      // Verify All apps category is restored
      await expect(page.getByRole('tab', { name: 'All apps' })).toHaveAttribute(
        'aria-selected',
        'true',
      );

      // Verify that all apps are visible again
      await expect(page.getByText('ASTs')).toBeVisible();
      await expect(page.getByText('Staff Scheduler')).toBeVisible();
      await expect(page.getByText('Bakery app')).toBeVisible();
      await expect(page.getByText('Legacy App')).toBeVisible();
    });

    test('should support keyboard navigation and activation workflow', async ({
      page,
    }) => {
      await page.goto('/apps');

      // Get references to tabs
      const allAppsTab = page.getByRole('tab', {
        name: 'All apps',
      });
      const analyticsTab = page.getByRole('tab', {
        name: 'Analytics',
      });

      // Verify initial state
      await expect(allAppsTab).toHaveAttribute('aria-selected', 'true');
      await expect(analyticsTab).toHaveAttribute('aria-selected', 'false');

      // Focus the tablist container and then try navigation
      const tablist = page.getByRole('tablist', {
        name: 'Filter apps by category',
      });
      await tablist.focus();

      // Try clicking on Analytics first to ensure the basic functionality works
      await analyticsTab.click();
      await expect(analyticsTab).toHaveAttribute('aria-selected', 'true');

      // Reset to All apps by clicking
      await allAppsTab.click();
      await expect(allAppsTab).toHaveAttribute('aria-selected', 'true');

      // Now test keyboard navigation by focusing the active tab directly
      await allAppsTab.focus();

      // Use a more explicit keyboard sequence
      await page.keyboard.press('ArrowRight');
      await page.waitForTimeout(100); // Small delay for state updates
      await page.keyboard.press('Enter');

      // Check if Analytics was activated
      await expect(analyticsTab).toHaveAttribute('aria-selected', 'true');
    });
  });

  test.describe('Responsive Design', () => {
    test('should work correctly on mobile viewport (360px)', async ({
      page,
    }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 360, height: 640 });
      await page.goto('/apps');

      // Check that category filter is still visible and functional
      await expect(
        page.getByRole('tablist', { name: 'Filter apps by category' }),
      ).toBeVisible();

      // Test filtering works on mobile - focus on layout/functionality, not announcements
      await page.getByRole('tab', { name: 'Stock management' }).click();

      // Verify content filtering works (core functionality)
      await expect(page.getByText('Inventory Manager')).toBeVisible();
    });

    test('should work correctly on tablet viewport (768px)', async ({
      page,
    }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.goto('/apps');

      // Check that category filter is visible and functional
      await expect(
        page.getByRole('tablist', { name: 'Filter apps by category' }),
      ).toBeVisible();

      // Test filtering works on tablet - focus on layout/functionality, not announcements
      await page.getByRole('tab', { name: 'Workforce' }).click();

      // Verify content filtering works (core functionality)
      await expect(page.getByText('Staff Scheduler')).toBeVisible();
    });
  });
});
