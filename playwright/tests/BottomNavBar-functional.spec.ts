import { test, expect } from '@playwright/test';

test.describe('BottomNavBar', () => {
  test('shows correct active tab state', async ({ page }) => {
    await page.goto('/');
    const homeTab = page.locator('[aria-label="Maps to Home"]');
    const appsTab = page.locator('[aria-label="Maps to Apps"]');

    await expect(homeTab).toHaveAttribute('aria-current', 'page');
    await expect(appsTab).not.toHaveAttribute('aria-current', 'page');
  });

  test('navigates with keyboard', async ({ page }) => {
    await page.goto('/');
    const homeTab = page.locator('[aria-label="Maps to Home"]');
    const appsTab = page.locator('[aria-label="Maps to Apps"]');

    await homeTab.focus();
    await expect(homeTab).toBeFocused();

    await appsTab.focus();
    await page.keyboard.press('Enter');
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(appsTab).toHaveAttribute('aria-current', 'page');
  });

  test('navigates with clicks', async ({ page }) => {
    await page.goto('/');
    const homeTab = page.locator('[aria-label="Maps to Home"]');
    const appsTab = page.locator('[aria-label="Maps to Apps"]');

    await appsTab.click();
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(appsTab).toHaveAttribute('aria-current', 'page');

    await homeTab.click();
    await expect(page).toHaveURL('/');
    await expect(homeTab).toHaveAttribute('aria-current', 'page');
  });
});
