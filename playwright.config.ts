import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './playwright/tests',
  fullyParallel: true,

  // Prevent accidental test.only commits in CI
  forbidOnly: !!process.env.CI,

  retries: process.env.CI ? 2 : 0,

  workers: process.env.CI ? 1 : undefined,

  reporter: process.env.CI
    ? [
        [
          'junit',
          {
            outputFile:
              process.env.PLAYWRIGHT_JUNIT_OUTPUT_NAME ||
              'test-results/playwright/results.xml',
          },
        ],
        ['html'],
      ]
    : 'html',

  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    // viewport for HHT devices
    viewport: { width: 360, height: 545 },
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  webServer: {
    command: process.env.CI ? 'npm run build && npm run start' : 'npm run dev',
    port: process.env.PORT ? parseInt(process.env.PORT) : 3000,
    reuseExistingServer: !process.env.CI,
  },
});
