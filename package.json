{"name": "store-hub-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:debug": "playwright test --debug", "test:e2e:headed": "playwright test --headed", "test:e2e:report": "playwright show-report", "test:e2e:ui": "playwright test --ui", "test:watch": "jest --watch", "setup-e2e": "npx playwright install --with-deps", "prepare": "husky"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "next lint --max-warnings=0"}, "dependencies": {"@coopdigital/foundations": "^1.9.0", "dayjs": "^1.11.13", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-variants": "^1.0.0"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/jest-axe": "^3.5.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.0-beta.3", "jest-junit": "^16.0.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}