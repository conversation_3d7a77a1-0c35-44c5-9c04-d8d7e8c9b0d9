This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.gitlab/
  merge_request_templates/
    Default.md
.husky/
  pre-commit
playwright/
  tests/
    apps.spec.ts
    BottomNavBar-functional.spec.ts
    home.spec.ts
    navigation.spec.ts
public/
  icons/
    icon-arrow-right.svg
src/
  app/
    actions/
      config.ts
    apps/
      page.tsx
    globals.css
    layout.tsx
    page.tsx
  components/
    icons/
      NavIcons.tsx
      UIIcons.tsx
    layout/
      header/
        __tests__/
          HeaderBar.test.tsx
        HeaderBar.tsx
      navigation/
        __tests__/
          BottomNavBar.test.tsx
          NavTab.test.tsx
        BottomNavBar.tsx
        NavErrorBoundary.tsx
        NavTab.tsx
    pages/
      apps/
        __tests__/
          AppItem.test.tsx
          AppsPage.test.tsx
        AppItem.tsx
        AppsPage.tsx
      home/
        HomePage.test.tsx
        HomePage.tsx
    HowDoISearchBar.tsx
  config/
    breakpoints.ts
    navigation.ts
  hooks/
    __tests__/
      useNavigation.test.ts
    useNavigation.ts
  utils/
    launchHowDoIApp.ts
.gitignore
.gitlab-ci.yml
.lintstagedrc.js
.prettierrc
eslint.config.mjs
jest.config.ts
jest.setup.ts
next.config.ts
package.json
playwright.config.ts
postcss.config.mjs
README.md
tailwind.config.ts
tsconfig.json
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path=".husky/pre-commit">
lint-staged
</file>

<file path="playwright/tests/apps.spec.ts">
import { test, expect } from '@playwright/test';

test.describe('Apps Page', () => {
  test('should load and display apps', async ({ page }) => {
    await page.goto('/apps');

    await expect(page.getByRole('heading', { name: 'Apps'})).toBeVisible();

    const appItemsCount = await page.locator('[data-testid^="app-item-"]').count();
    expect(appItemsCount).toBeGreaterThan(0);
  });
});
</file>

<file path="playwright/tests/BottomNavBar-functional.spec.ts">
import { test, expect } from '@playwright/test';

test.describe('BottomNavBar', () => {
  test('shows correct active tab state', async ({ page }) => {
    await page.goto('/');
    const homeTab = page.locator('[aria-label="Maps to Home"]');
    const appsTab = page.locator('[aria-label="Maps to Apps"]');

    await expect(homeTab).toHaveAttribute('aria-current', 'page');
    await expect(appsTab).not.toHaveAttribute('aria-current', 'page');
  });

  test('navigates with keyboard', async ({ page }) => {
    await page.goto('/');
    const homeTab = page.locator('[aria-label="Maps to Home"]');
    const appsTab = page.locator('[aria-label="Maps to Apps"]');

    await homeTab.focus();
    await expect(homeTab).toBeFocused();

    await appsTab.focus();
    await page.keyboard.press('Enter');
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(appsTab).toHaveAttribute('aria-current', 'page');
  });

  test('navigates with clicks', async ({ page }) => {
    await page.goto('/');
    const homeTab = page.locator('[aria-label="Maps to Home"]');
    const appsTab = page.locator('[aria-label="Maps to Apps"]');

    await appsTab.click();
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(appsTab).toHaveAttribute('aria-current', 'page');

    await homeTab.click();
    await expect(page).toHaveURL('/');
    await expect(homeTab).toHaveAttribute('aria-current', 'page');
  });
});
</file>

<file path="public/icons/icon-arrow-right.svg">
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.86664 11.3202H19.4358L12.2053 4.32216C12.1394 4.25894 12.0871 4.18372 12.0514 4.10085C12.0158 4.01797 11.9974 3.92908 11.9974 3.8393C11.9974 3.74953 12.0158 3.66064 12.0514 3.57776C12.0871 3.49489 12.1394 3.41967 12.2053 3.35645C12.3369 3.22978 12.515 3.15869 12.7006 3.15869C12.8863 3.15869 13.0644 3.22978 13.196 3.35645L21.6282 11.5174C21.6941 11.5806 21.7463 11.6558 21.782 11.7387C21.8177 11.8216 21.836 11.9105 21.836 12.0003C21.836 12.09 21.8177 12.1789 21.782 12.2618C21.7463 12.3447 21.6941 12.4199 21.6282 12.4831L13.196 20.6441C13.0652 20.7697 12.8885 20.8406 12.7042 20.8413C12.6117 20.8418 12.52 20.8247 12.4344 20.7908C12.3488 20.757 12.2709 20.7071 12.2053 20.6441C12.1394 20.5809 12.0871 20.5056 12.0514 20.4228C12.0158 20.3399 11.9974 20.251 11.9974 20.1612C11.9974 20.0714 12.0158 19.9826 12.0514 19.8997C12.0871 19.8168 12.1394 19.7416 12.2053 19.6784L19.4429 12.6803H2.86664C2.68027 12.6803 2.50154 12.6087 2.36977 12.4812C2.23799 12.3536 2.16396 12.1806 2.16396 12.0003C2.16396 11.8199 2.23799 11.6469 2.36977 11.5194C2.50154 11.3918 2.68027 11.3202 2.86664 11.3202Z" fill="#0F8482"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.3116 3.24391C13.1492 3.08769 12.9296 3 12.7006 3C12.4718 3 12.2522 3.08764 12.0898 3.24378C12.0086 3.32176 11.9441 3.41452 11.9001 3.51673C11.8561 3.61895 11.8334 3.72858 11.8334 3.8393C11.8334 3.95003 11.8561 4.05966 11.9001 4.16187C11.944 4.26398 12.0084 4.35667 12.0895 4.43459L19.04 11.1615H2.86664C2.63679 11.1615 2.41636 11.2499 2.25383 11.4072C2.0913 11.5645 2 11.7778 2 12.0003C2 12.2227 2.0913 12.4361 2.25383 12.5934C2.41636 12.7507 2.63679 12.839 2.86664 12.839H19.0468L12.0898 19.5657C12.0087 19.6436 11.9441 19.7365 11.9001 19.8387C11.8561 19.9409 11.8334 20.0505 11.8334 20.1612C11.8334 20.272 11.8561 20.3816 11.9001 20.4838C11.9441 20.586 12.0086 20.6788 12.0898 20.7567C12.1708 20.8345 12.2668 20.896 12.3724 20.9377C12.478 20.9795 12.591 21.0006 12.7051 21C12.9324 20.999 13.1501 20.9116 13.3115 20.7567L21.7436 12.5958C21.8248 12.5179 21.8894 12.4249 21.9333 12.3228C21.9773 12.2206 22 12.111 22 12.0003C22 11.8895 21.9773 11.7799 21.9333 11.6777C21.8894 11.5756 21.825 11.4829 21.7439 11.405L13.3116 3.24391ZM13.0804 3.46899L21.5123 11.6296C21.5627 11.6781 21.6033 11.7362 21.6307 11.7997C21.658 11.8633 21.6721 11.9314 21.6721 12.0003C21.6721 12.0691 21.658 12.1372 21.6307 12.2008C21.6033 12.2643 21.5632 12.322 21.5127 12.3705L13.0806 20.5314C12.9803 20.6275 12.8446 20.682 12.7035 20.6826C12.6326 20.683 12.562 20.6699 12.4964 20.6439C12.4308 20.618 12.3711 20.5797 12.3207 20.5314C12.2702 20.4829 12.2301 20.4253 12.2028 20.3617C12.1754 20.2982 12.1614 20.2301 12.1614 20.1612C12.1614 20.0924 12.1754 20.0242 12.2028 19.9607C12.2301 19.8972 12.2702 19.8395 12.3207 19.791L19.839 12.5217H2.86664C2.72376 12.5217 2.58673 12.4667 2.4857 12.3689C2.38467 12.2712 2.32792 12.1385 2.32792 12.0003C2.32792 11.862 2.38467 11.7294 2.4857 11.6316C2.58673 11.5338 2.72376 11.4789 2.86664 11.4789H19.8317L12.3212 4.20995C12.2707 4.16148 12.2301 4.10336 12.2028 4.03982C12.1754 3.97628 12.1614 3.90813 12.1614 3.8393C12.1614 3.77047 12.1754 3.70233 12.2028 3.63879C12.2301 3.57525 12.2702 3.51758 12.3207 3.46911C12.4216 3.372 12.5583 3.31737 12.7006 3.31737C12.8429 3.31737 12.9795 3.37201 13.0804 3.46899Z" fill="#0F8482"/>
</svg>
</file>

<file path="src/components/icons/NavIcons.tsx">
import React from 'react';

type IconProps = {
  className?: string;
  size?: number;
  isSelected?: boolean;
  title?: string;
};

export const HomeIcon = ({
  className,
  isSelected,
  size = 28,
  title = 'Home Icon',
}: IconProps) => (
  <div className={className} style={{ width: size, height: size }}>
    <svg
      width="100%"
      height="100%"
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 26 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>{title}</title>
      <path
        d="M2.64624 10.485L12.8001 0.990479L22.9539 10.485V23.1443H2.64624V10.485Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <path
        d="M24.8 11.9326L12.8 0.855713"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M0.800048 11.9326L12.8 0.855713"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.8 14.0002H18.8V23.0002H12.8V14.0002Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
        fill={isSelected ? '#DDE9ED' : 'none'}
      />
    </svg>
  </div>
);

export const AppsIcon = ({
  className,
  isSelected,
  size = 30,
  title = 'Apps Icon',
}: IconProps) => (
  <div className={className} style={{ width: size, height: size }}>
    <svg
      width="100%"
      height="100%"
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>{title}</title>
      <rect
        x="6"
        y="6"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <rect
        x="18"
        y="6"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <rect
        x="6"
        y="18"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <rect
        x="18"
        y="18"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={!isSelected ? 'currentColor' : 'none'}
      />
    </svg>
  </div>
);
</file>

<file path="src/components/icons/UIIcons.tsx">
import React from 'react';

type IconProps = {
  className?: string;
  size?: number;
  title?: string;
};

export const SearchIcon = ({
  className,
  size = 24,
  title = 'Search Icon',
}: IconProps) => (
  <div className={className} style={{ width: size, height: size }}>
    <svg
      width="100%"
      height="100%"
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>{title}</title>
      <path
        d="M22.7836 21.6086L16.9988 15.8237C18.3546 14.1967 19.0777 12.2082 19.0777 10.0388C19.0777 5.06748 15.0102 1 10.0388 1C5.06748 1 1 5.06748 1 10.0388C1 15.0102 5.06748 19.0777 10.0388 19.0777C12.1178 19.0777 14.1967 18.3546 15.8237 16.9988L21.6086 22.7836C21.7893 22.9644 21.9701 23.0548 22.2413 23.0548C22.5125 23.0548 22.6932 22.9644 22.874 22.7836C23.0548 22.4221 23.0548 21.8797 22.7836 21.6086ZM17.3603 10.0388C17.3603 14.1063 14.1063 17.3603 10.0388 17.3603C5.97137 17.3603 2.71738 14.1063 2.71738 10.0388C2.71738 5.97137 5.97137 2.71738 10.0388 2.71738C14.0159 2.71738 17.3603 5.97137 17.3603 10.0388Z"
        fill="white"
      />
    </svg>
  </div>
);
</file>

<file path="src/components/layout/header/__tests__/HeaderBar.test.tsx">
import { HeaderBar } from '@/components/layout/header/HeaderBar';
import { render } from '@testing-library/react';

describe('HeaderBar', () => {
  it('renders the header component with children', () => {
    const mockChildren = (<div data-testid='mock-test-id'>Mock Content</div>);
    const { getByTestId } = render(<HeaderBar>{mockChildren}</HeaderBar>);
    expect(getByTestId('mock-test-id')).toBeInTheDocument();
  });
});
</file>

<file path="src/components/layout/navigation/__tests__/BottomNavBar.test.tsx">
import { render, screen } from '@testing-library/react';
import { BottomNavBar } from '@/components/layout/navigation/BottomNavBar';

jest.mock('next/navigation', () => ({
  usePathname: jest.fn(() => '/'),
}));

jest.mock('@/hooks/useNavigation', () => ({
  useNavigation: jest.fn(() => ({
    isTabActive: jest.fn((href: string) => href === '/'),
  })),
}));

jest.mock('@/config/navigation', () => ({
  navItems: [
    {
      href: '/',
      label: 'Home',
      icon: () => <div data-testid="home-icon">Home Icon</div>,
    },
    {
      href: '/apps',
      label: 'Apps',
      icon: () => <div data-testid="apps-icon">Apps Icon</div>,
    },
  ],
}));

describe('BottomNavBar', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the mobile navigation', () => {
    render(<BottomNavBar />);

    const nav = screen.getByRole('navigation');
    expect(nav).toBeInTheDocument();
    expect(nav).toHaveAttribute('aria-label', 'Main navigation');

    expect(screen.getByLabelText('Maps to Home')).toBeInTheDocument();
    expect(screen.getByLabelText('Maps to Apps')).toBeInTheDocument();
  });

  it('should correctly identify the active tab', () => {
    render(<BottomNavBar />);

    expect(screen.getByLabelText('Maps to Home')).toHaveAttribute(
      'aria-current',
      'page',
    );
    expect(screen.getByLabelText('Maps to Apps')).not.toHaveAttribute(
      'aria-current',
      'page',
    );
  });
});
</file>

<file path="src/components/layout/navigation/__tests__/NavTab.test.tsx">
import { render, screen } from '@testing-library/react';
import { NavTab } from '@/components/layout/navigation/NavTab';
import type { NavItem } from '@/config/navigation';
import '@testing-library/jest-dom';

const mockHomeItem: NavItem = {
  href: '/',
  label: 'Home',
  icon: ({ className }) => (
    <svg className={className} data-testid="home-icon" />
  ),
};

describe('NavTab', () => {
  it('should render with selected styling including background', () => {
    render(<NavTab item={mockHomeItem} isSelected={true} />);

    const link = screen.getByRole('link', { name: /maps to home/i });
    expect(link).toBeInTheDocument();

    const iconWrapper = link.querySelector('div[class*="bg-slate-200"]');
    expect(iconWrapper).toBeInTheDocument();
  });

  it('should render without selected styling when not selected', () => {
    render(<NavTab item={mockHomeItem} isSelected={false} />);
    const link = screen.getByRole('link', { name: /maps to home/i });
    expect(link).toBeInTheDocument();

    const iconWrapper = link.querySelector('div[class*="bg-slate-200"]');
    expect(iconWrapper).not.toBeInTheDocument();
  });
});
</file>

<file path="src/components/layout/navigation/BottomNavBar.tsx">
'use client';

import { useNavigation } from '@/hooks/useNavigation';
import { NavTab } from '@/components/layout/navigation/NavTab';
import { navItems } from '@/config/navigation';

export function BottomNavBar() {
  const { isTabActive } = useNavigation();

  const navClass =
    'flex h-full w-full items-center justify-evenly px-4';

  return (
    <header className="fixed bottom-0 left-0 right-0 z-50 h-20 border-t border-gray-200 bg-white shadow-nav-bar">
      <nav role="navigation" aria-label="Main navigation" className={navClass}>
        {navItems.map((item) => (
          <NavTab
            key={item.href}
            item={item}
            isSelected={isTabActive(item.href)}
            iconClassName="h-6 w-6 sm:h-8 sm:w-8"
          />
        ))}
      </nav>
    </header>
  );
}
</file>

<file path="src/components/layout/navigation/NavErrorBoundary.tsx">
'use client';

import React, { Component, ReactNode } from 'react';

interface NavErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface NavErrorBoundaryProps {
  children: ReactNode;
}

function NavFallback({ onRetry }: { onRetry: () => void }) {
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 h-20 bg-red-50 border-t border-red-200 flex items-center justify-center">
      <div className="text-center">
        <p className="text-sm text-red-600 mb-2">
          Navigation temporarily unavailable
        </p>
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 transition-colors"
          aria-label="Retry navigation"
        >
          Try Again
        </button>
      </div>
    </div>
  );
}

export class NavErrorBoundary extends Component<
  NavErrorBoundaryProps,
  NavErrorBoundaryState
> {
  constructor(props: NavErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): NavErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Navigation Error:', error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      return <NavFallback onRetry={this.handleRetry} />;
    }

    return this.props.children;
  }
}
</file>

<file path="src/components/layout/navigation/NavTab.tsx">
import Link from 'next/link';
import { tv, type VariantProps } from 'tailwind-variants';
import type { NavItem } from '@/config/navigation';

const tab = tv({
  slots: {
    link: 'flex flex-col items-center justify-center rounded-lg p-2 text-xs text-gray-600 no-underline transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2',
    iconWrapper: 'rounded-full py-1 transition-all duration-200',
    icon: 'h-7 w-7 transition-colors',
    label: 'text-xs font-medium',
  },
  variants: {
    isSelected: {
      true: {
        link: 'text-gray-900',
        iconWrapper: 'bg-slate-200 px-16',
        icon: 'text-gray-900',
        label: 'font-semibold text-gray-900 pt-1',
      },
      false: {
        link: 'text-gray-600 hover:bg-gray-100 hover:text-gray-800',
        iconWrapper: 'bg-transparent px-4',
        icon: 'text-gray-600',
        label: 'text-gray-600 pt-1',
      },
    },
    size: {
      default: {
        icon: 'h-8 w-8',
      },
      large: {
        icon: 'h-10 w-10',
      },
      small: {
        icon: 'h-6 w-6',
      },
    },
  },
  defaultVariants: {
    size: 'default',
  },
});

interface NavTabProps extends VariantProps<typeof tab> {
  item: NavItem;
  isSelected: boolean;
  iconClassName?: string;
}

export function NavTab({ item, isSelected, size, iconClassName }: NavTabProps) {
  const { link, iconWrapper, icon, label } = tab({ isSelected, size });
  const renderIcon = () => {
    const IconComponent = item.icon;
    return (
      <div className="flex w-24 justify-center">
        <div className={iconWrapper()}>
          <IconComponent
            className={`${icon()} ${iconClassName || ''}`}
            width={32}
            height={32}
            isSelected={isSelected}
            aria-hidden="true"
          />
        </div>
      </div>
    );
  };

  return (
    <Link
      href={item.href}
      className={link()}
      aria-current={isSelected ? 'page' : undefined}
      aria-label={`Maps to ${item.label}`}
    >
      {renderIcon()}
      <span className={label()}>{item.label}</span>
    </Link>
  );
}
</file>

<file path="src/components/pages/apps/__tests__/AppItem.test.tsx">
import { AppItem } from "@/components/pages/apps/AppItem";
import { render, screen } from "@testing-library/react";

describe("AppItem", () => {
  const mockAppItem: AppItem = {
    appName: "Test App",
    appIcon: "/icons/test-icon.png",
    appLaunchUrl: "launch://testapp" //note this is not use yet in the component
  };

  it("renders app item correctly", () => {
    render(<AppItem appItem={mockAppItem} />);
    expect(screen.getByText(mockAppItem.appName)).toBeInTheDocument();
    expect(screen.getByAltText("Test App Icon")).toHaveAttribute("src", mockAppItem.appIcon);
  });
});
</file>

<file path="src/components/pages/apps/__tests__/AppsPage.test.tsx">
import { render, screen } from '@testing-library/react';
import { AppsPage } from '@/components/pages/apps/AppsPage';

describe('AppsPage', () => {
  it('renders the apps page', () => {
    const { container } = render(<AppsPage />);
    expect(container).toBeInTheDocument();
  });

  it('renders the header', () => {
    render(<AppsPage />);
    expect(screen.getByText('Apps'))
  });

  it('renders app items', () => {
    render(<AppsPage/>);
    const appItems = screen.getAllByTestId(/app-item-/);
    expect(appItems.length).toBeGreaterThan(0);
  });
});
</file>

<file path="src/components/pages/home/<USER>">
import { HomePage } from '@/components/pages/home/<USER>';
import { render } from '@testing-library/react';

describe('HomePage', () => {
  it('renders the home page', () => {
    const { container } = render(<HomePage />);
    expect(container).toBeInTheDocument();
  });
});
</file>

<file path="src/config/breakpoints.ts">
export const breakpoints = {
  hht: 360,
  tablet: 600,
  tabletLg: 768,
} as const;

export const breakpointsPx = {
  hht: `${breakpoints.hht}px`,
  tablet: `${breakpoints.tablet}px`,
  tabletLg: `${breakpoints.tabletLg}px`,
} as const;

export const viewports = {
  mobile: { width: breakpoints.hht, height: 640 },
  tablet: { width: breakpoints.tablet, height: 1024 },
  tabletLg: { width: breakpoints.tabletLg, height: 1024 },
} as const;
</file>

<file path="src/config/navigation.ts">
export type NavItem = {
  href: string;
  label: string;
  icon: React.FC<{
    width?: number;
    height?: number;
    className?: string;
    isSelected?: boolean;
  }>;
};

import { AppsIcon, HomeIcon } from '@/components/icons/NavIcons';

export const navItems: NavItem[] = [
  {
    href: '/',
    label: 'Home',
    icon: HomeIcon,
  },
  {
    href: '/apps',
    label: 'Apps',
    icon: AppsIcon,
  },
];
</file>

<file path="src/hooks/__tests__/useNavigation.test.ts">
import { renderHook } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { useNavigation } from '../useNavigation';

jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('useNavigation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('isTabActive', () => {
    it('should return true for the root path when href is "/"', () => {
      mockUsePathname.mockReturnValue('/');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/')).toBe(true);
    });

    it('should return false for a nested path when href is "/"', () => {
      mockUsePathname.mockReturnValue('/some-page');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/')).toBe(false);
    });

    it('should return true when current path starts with tab href', () => {
      mockUsePathname.mockReturnValue('/apps');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(true);
    });

    it('should return true for nested paths of a non-root href', () => {
      mockUsePathname.mockReturnValue('/apps/some-app');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(true);
    });

    it('should return false when current path does not match', () => {
      mockUsePathname.mockReturnValue('/other');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(false);
    });

    it('should not activate non-root tabs for the root path', () => {
      mockUsePathname.mockReturnValue('/');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(false);
    });
  });

  describe('pathname', () => {
    it('should return current pathname', () => {
      const testPath = '/current/path';
      mockUsePathname.mockReturnValue(testPath);

      const { result } = renderHook(() => useNavigation());

      expect(result.current.pathname).toBe(testPath);
    });
  });
});
</file>

<file path="src/hooks/useNavigation.ts">
'use client';

import { useCallback } from 'react';
import { usePathname } from 'next/navigation';

export function useNavigation() {
  const pathname = usePathname();

  const isTabActive = useCallback(
    (href: string) => {
      if (href === '/') {
        return pathname === '/';
      }
      return pathname.startsWith(href);
    },
    [pathname],
  );

  return {
    pathname,
    isTabActive,
  };
}
</file>

<file path="src/utils/launchHowDoIApp.ts">
export function launchHowDoIApp(question: string): void {
  if (!question || !question.trim()) {
    return;
  }
}
</file>

<file path=".lintstagedrc.js">
// required for 'next lint' command to work with lint-staged
// https://nextjs.org/docs/pages/api-reference/config/eslint#running-lint-on-staged-files

// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require('path')

const buildEslintCommand = (filenames) =>
    `next lint --fix --file ${filenames
        .map((f) => path.relative(process.cwd(), f))
        .join(' --file ')}`

module.exports = {
    '*.{js,jsx,ts,tsx}': [buildEslintCommand],
}
</file>

<file path=".prettierrc">
{
  "singleQuote": true,
  "tabWidth": 2
}
</file>

<file path="eslint.config.mjs">
import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript", "prettier"),
];

export default eslintConfig;
</file>

<file path="jest.setup.ts">
import '@testing-library/jest-dom'
</file>

<file path="playwright/tests/navigation.spec.ts">
import { test, expect } from '@playwright/test';

test.describe('Main Navigation', () => {
  test('should allow normal navigation between tabs', async ({ page }) => {
    await page.goto('/');

    // Start at home, go to apps
    await page.click('[aria-label="Maps to Apps"]');
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(page.locator('main').getByText('Apps')).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Apps"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
    await expect(
      page.locator('[aria-label="Maps to Home"]'),
    ).not.toHaveAttribute('aria-current', 'page');

    // Go back to home
    await page.click('[aria-label="Maps to Home"]');
    await expect(page).toHaveURL('/');
    await expect(
      page.getByRole('textbox', { name: 'Ask How Do I question' }),
    ).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Home"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
    await expect(
      page.locator('[aria-label="Maps to Apps"]'),
    ).not.toHaveAttribute('aria-current', 'page');
  });

  test('should stay on /apps page when refreshed', async ({ page }) => {
    // Go to /apps first by clicking
    await page.goto('/');
    await page.click('[aria-label="Maps to Apps"]');
    await expect(page).toHaveURL(/\/apps\/?/);

    // Reload the page
    await page.reload();

    // It should stay on apps page
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(page.locator('main').getByText('Apps')).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Apps"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
    await expect(
      page.locator('[aria-label="Maps to Home"]'),
    ).not.toHaveAttribute('aria-current', 'page');
  });

  test('direct navigation to /apps should work correctly', async ({ page }) => {
    // Go directly to /apps
    await page.goto('/apps');

    // It should stay on apps page and select the correct tab
    await expect(page).toHaveURL(/\/apps\/?/);
    await expect(page.locator('main').getByText('Apps')).toBeVisible();
    await expect(page.locator('[aria-label="Maps to Apps"]')).toHaveAttribute(
      'aria-current',
      'page',
    );
  });
});
</file>

<file path="src/components/layout/header/HeaderBar.tsx">
export function HeaderBar({ children }: { children: React.ReactNode }) {
  return (
    <header className="fixed top-0 right-0 left-0 p-4 items-center bg-white shadow-standard">
      {children}
    </header>
  )
}
</file>

<file path="src/components/pages/apps/AppItem.tsx">
import Image from "next/image";

// TODO: Move this type somewhere else.
// For now it is ok in here but this type would be used for the list of apps coming getting posted into iframe.
export type AppItem = {
  appName: string,
  appLaunchUrl: string,
  appIcon: string
}

export function AppItem({appItem}: {appItem: AppItem}) {
  return (  
    <div role="button" data-testid={`app-item-${appItem.appName}`} className="w-full h-[86px] flex items-center pr-4 gap-4 bg-white rounded-[24px] shadow-standard">
      {/* This will likely be a base64 encoded image and you can't use the Image component to render those hence the eslint disable */}
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img src={appItem.appIcon} alt={`${appItem.appName} Icon`} className="w-[75px] h-[75px]" />
      <span className="text-base font-medium text-grey-900">{appItem.appName}</span>
      <Image
        src="/icons/icon-arrow-right.svg"
        alt="arrow icon"
        width={24}
        height={24}
        className="ml-auto"
      />
    </div>
  )
}
</file>

<file path="src/components/pages/apps/AppsPage.tsx">
import { HeaderBar } from "@/components/layout/header/HeaderBar";
import { AppItem } from "@/components/pages/apps/AppItem";

export function AppsPage() {

  // Temporarily construct some dummy apps.
  const appList: AppItem[] = [...Array(10)].map((_, i) => (
    {
      appName: `App ${i+1}`,
      appIcon: "/icons/appicon.png", // this will eventually be base64 encoded image hopefully
      appLaunchUrl: "#" // not used currently
    }
  ));

  return (
    <>
      <HeaderBar>
        <h1 className="text-center w-full font-semibold text-lg">
          Apps
        </h1>
      </HeaderBar>
      <div className="flex-1 overflow-y-auto overflow-x-hidden h-screen font-semibold text-2xl pt-18 pb-22">
        <div className="flex flex-col items-center gap-y-2 ml-[10px] mr-[10px]">
          {/* TODO: Replace with proper logic to render apps. This is temporary work to build upon. */}
          {appList.map((appItem) => (
            <AppItem key={appItem.appName} appItem={appItem}/>
          ))}
          </div>
      </div>
    </>
  );
}
</file>

<file path="next.config.ts">
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  output: 'standalone',
  devIndicators: false,
};

export default nextConfig;
</file>

<file path="postcss.config.mjs">
const config = {
  plugins: ['@tailwindcss/postcss'],
};

export default config;
</file>

<file path="src/app/actions/config.ts">
'use server';

export interface FeatureFlags {
  howDoISearchEnabled: boolean;
}

export async function getFeatureFlags(): Promise<FeatureFlags> {
  return {
    howDoISearchEnabled:
      process.env.NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED !== 'false',
  };
}
</file>

<file path=".gitignore">
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/test-results

# playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode
.idea
.DS_Store
</file>

<file path="jest.config.ts">
import type { Config } from 'jest'
import nextJest from 'next/jest.js'

const createJestConfig = nextJest({
  dir: './',
})

const config: Config = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  testPathIgnorePatterns: ['<rootDir>/playwright/'],
  reporters: [
    'default',
    ['jest-junit', { outputDirectory: 'test-results' }]
  ],
  coverageReporters: ['cobertura', 'text-summary', 'html'],
  collectCoverageFrom: [
    '<rootDir>/src/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts'
  ],
  coverageDirectory: 'coverage',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};

export default createJestConfig(config)
</file>

<file path=".gitlab/merge_request_templates/Default.md">
## 📝 Description

<!-- Provide a short summary of the changes -->

## 🛠️ Changes Made

<!-- List specific changes or updates made to files -->

## 🧩 Type of Change

<!-- Check the relevant box(es) to indicate the type of change -->

- [ ] Bug fix 🐛
- [ ] Feature changes ✨
- [ ] Refactor 🔨
- [ ] Documentation 📚
- [ ] Other (please describe):

## 🧪 How Has This Been Tested?

<!-- Replace any slashes in the branch name with dashes -->
### [📋 Test coverage report](https://store-hub-app-c46652.gitlab.io/%{source_branch}/coverage/)

<!-- Describe the testing used to verify the changes -->

- [ ] Have automated tests been added where applicable? 🤖
</file>

<file path="src/app/apps/page.tsx">
import { AppsPage } from '@/components/pages/apps/AppsPage';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Apps - Store Hub',
};

export default function Apps() {
  return <AppsPage/>
}
</file>

<file path="src/components/pages/home/<USER>">
import { HowDoISearchBar } from '@/components/HowDoISearchBar';

interface HomePageProps {
  howDoISearchEnabled?: boolean;
}

export function HomePage({ howDoISearchEnabled = true }: HomePageProps) {
  return (
    <div className="w-screen h-screen flex flex-col justify-end pb-28">
      <div className="flex justify-center px-4">
        <div className="w-full max-w-[600px]">
          <HowDoISearchBar isFeatureEnabled={howDoISearchEnabled} />
        </div>
      </div>
    </div>
  );
}
</file>

<file path="tsconfig.json">
{
  "compilerOptions": {
    "target": "es2020",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": [
      "jest",
      "@testing-library/jest-dom",
      "node",
      "@playwright/test"
    ],
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "jest.setup.ts", "playwright/**/*.ts"],
  "exclude": ["node_modules"]
}
</file>

<file path="src/app/layout.tsx">
import type { Metadata, Viewport } from 'next';
import { BottomNavBar } from '@/components/layout/navigation/BottomNavBar';
import { NavErrorBoundary } from '@/components/layout/navigation/NavErrorBoundary';
import './globals.css';

export const metadata: Metadata = {
  title: 'Store Hub',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body
        className={`antialiased h-full flex flex-col bg-background-blue text-foreground-light`}
      >
        <main id="main-content" className="flex-grow">
          {children}
        </main>
        <NavErrorBoundary>
          <BottomNavBar />
        </NavErrorBoundary>
      </body>
    </html>
  );
}
</file>

<file path="README.md">
# store-hub-app

[![coverage](https://gitlab.com/coopeng/retail/stores/store-hub/store-hub-app/badges/main/coverage.svg)](https://coopeng.gitlab.io/retail/stores/store-hub/store-hub-app/coverage/)

This repo contains the code for the Store Hub app. This is a Next.js app that handles the user interface of Store Hub.

## Getting Started

To initialise your local environment, clone this repo and run:

```bash
npm install
npm run prepare
```

To run the app in development mode run:

```bash
npm run dev
```

The app will be accessible at [http://localhost:3000](http://localhost:3000).

## Testing and CI/CD

### Testing Framework

- **Unit Tests**: Jest with jsdom environment for component and utility testing
- **E2E Tests**: Playwright for end-to-end testing across multiple browsers (Chromium, Firefox, WebKit)

### Setup

Before running E2E tests Playwright must be installed by running:
```bash
npm run setup:e2e
```

### Available Test Scripts
```bash
# Run Jest unit tests
npm test

# Run Playwright E2E tests
npm run test:e2e

# Run E2E tests in headed mode (with browser UI)
npm run test:e2e:headed

# Run E2E tests in debug mode
npm run test:e2e:debug

# View Playwright test report
npm run test:e2e:report

# Open Playwright UI mode
npm run test:e2e:ui
```

### Testing Metrics

Test results and code coverage are integrated with the Gitlab merge request feature. A full code coverage report can be viewed using the link in the default merge request description template.
</file>

<file path="tailwind.config.ts">
import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  plugins: [],
};

export default config;
</file>

<file path="playwright/tests/home.spec.ts">
import { test, expect } from '@playwright/test';
import { viewports } from '../../src/config/breakpoints';

test.describe('Home Page', () => {
  test('should load and display main elements', async ({ page }) => {
    await page.goto('/');

    const searchInput = page.getByRole('textbox', {
      name: 'Ask How Do I question',
    });
    await expect(searchInput).toBeVisible();
  });

  test.describe('How Do I Search Bar', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/');
    });

    test('should display and function correctly', async ({ page }) => {
      const searchInput = page.getByRole('textbox', {
        name: 'Ask How Do I question',
      });
      const searchButton = page.getByRole('button', { name: 'Search' });

      await expect(searchInput).toBeVisible();
      await expect(searchButton).toBeVisible();
      await expect(searchInput).toHaveAttribute(
        'placeholder',
        'Ask How Do I...',
      );

      await expect(searchButton).toBeDisabled();

      await searchInput.fill('how do I reset my password');
      await expect(searchButton).toBeEnabled();

      await searchInput.clear();
      await expect(searchButton).toBeDisabled();

      await searchInput.fill('   ');
      await expect(searchButton).toBeDisabled();
    });

    test('should work on mobile and tablet', async ({ page }) => {
      const searchInput = page.getByRole('textbox', {
        name: 'Ask How Do I question',
      });
      const searchButton = page.getByRole('button', { name: 'Search' });

      await page.setViewportSize(viewports.mobile);
      await expect(searchInput).toBeVisible();
      await expect(searchButton).toBeVisible();

      await page.setViewportSize(viewports.tablet);
      await expect(searchInput).toBeVisible();
      await expect(searchButton).toBeVisible();
    });
  });
});
</file>

<file path="src/components/HowDoISearchBar.tsx">
'use client';

import React, { useState, FormEvent } from 'react';
import { tv, type VariantProps } from 'tailwind-variants';
import { launchHowDoIApp } from '@/utils/launchHowDoIApp';
import { SearchIcon } from '@/components/icons/UIIcons';

const searchBar = tv({
  slots: {
    container: 'w-full flex items-center justify-center',
    form: 'flex items-center w-[328px] sm:w-[500px] md:w-[600px] h-[44px] rounded-[4px] shadow-standard overflow-hidden border',
    input:
      'flex-grow text-base font-normal bg-transparent outline-none px-3 py-2',
    button:
      'w-[44px] h-[44px] flex items-center justify-center flex-shrink-0 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-r-[4px] cursor-pointer',
    icon: 'w-5 h-5',
  },
  variants: {
    enabled: {
      true: {
        container: 'block',
        form: 'border-border-primary bg-white',
        input: 'text-text-primary text-base placeholder:text-border-primary',
        button: 'bg-primary hover:bg-primary-hover focus:ring-primary',
      },
      false: {
        container: 'hidden',
      },
    },
    submitting: {
      true: {
        button: 'opacity-50 cursor-not-allowed',
        input: 'opacity-50',
      },
      false: {},
    },
  },
  defaultVariants: {
    enabled: true,
    submitting: false,
  },
});

interface HowDoISearchBarProps extends VariantProps<typeof searchBar> {
  isFeatureEnabled?: boolean;
  placeholder?: string;
}

export function HowDoISearchBar({
  enabled = true,
  isFeatureEnabled = true,
  placeholder = 'Ask How Do I...',
}: HowDoISearchBarProps) {
  const [searchText, setSearchText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const shouldShow = isFeatureEnabled && enabled;

  const { container, form, input, button, icon } = searchBar({
    enabled: shouldShow,
    submitting: isSubmitting,
  });

  const handleSubmit = (event: FormEvent) => {
    event.preventDefault();

    if (!searchText.trim() || isSubmitting) {
      return;
    }

    setIsSubmitting(true);

    try {
      launchHowDoIApp(searchText.trim());
      setSearchText('');
    } catch (error) {
      console.error('Failed to launch HowDoI app:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={container()}>
      <form
        onSubmit={handleSubmit}
        className={form()}
        role="search"
        aria-label="How Do I search"
      >
        <input
          type="text"
          placeholder={placeholder}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className={input()}
          aria-label="Ask How Do I question"
          disabled={isSubmitting}
          autoComplete="off"
          spellCheck="false"
        />
        <button
          type="submit"
          className={button()}
          aria-label="Search"
          disabled={!searchText.trim() || isSubmitting}
        >
          <SearchIcon className={icon()} size={24} />
        </button>
      </form>
    </div>
  );
}
</file>

<file path="package.json">
{
  "name": "store-hub-app",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "jest",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:debug": "playwright test --debug",
    "test:e2e:headed": "playwright test --headed",
    "test:e2e:report": "playwright show-report",
    "test:e2e:ui": "playwright test --ui",
    "test:watch": "jest --watch",
    "setup-e2e": "npx playwright install --with-deps",
    "prepare": "husky"
  },
  "lint-staged": {
    "src/**/*.{js,jsx,ts,tsx}": "next lint --max-warnings=0"
  },
  "dependencies": {
    "@coopdigital/foundations": "^1.9.0",
    "clsx": "^2.1.1",
    "next": "15.3.3",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "tailwind-variants": "^1.0.0"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@playwright/test": "^1.52.0",
    "@tailwindcss/postcss": "^4",
    "@testing-library/jest-dom": "^6.6.3",
    "@testing-library/react": "^16.3.0",
    "@types/jest": "^29.5.14",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "eslint": "^9",
    "eslint-config-next": "15.3.3",
    "eslint-config-prettier": "^10.1.5",
    "husky": "^9.1.7",
    "jest": "^29.7.0",
    "jest-environment-jsdom": "^30.0.0-beta.3",
    "jest-junit": "^16.0.0",
    "lint-staged": "^16.1.0",
    "prettier": "^3.5.3",
    "tailwindcss": "^4",
    "ts-node": "^10.9.2",
    "typescript": "^5"
  }
}
</file>

<file path="src/app/page.tsx">
import { HomePage } from '@/components/pages/home/<USER>';
import { getFeatureFlags } from '@/app/actions/config';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Home - Store Hub',
};

export default async function Home() {
  const { howDoISearchEnabled } = await getFeatureFlags();
  return <HomePage howDoISearchEnabled={howDoISearchEnabled} />;
}
</file>

<file path="src/app/globals.css">
@import 'tailwindcss';

@theme {
  --color-white: #ffffff;
  --color-foreground-light: #171717;
  --color-background-dark: #0a0a0a;
  --color-foreground-dark: #ededed;
  --color-background-blue: #eef3fc;

  --color-border-primary: #6e6e6e;
  --color-text-primary: #000000;
  --color-primary: #0f8482;
  --color-primary-hover: #0d766e;

  --breakpoint-hht: 360px;
  --breakpoint-tablet: 600px;
  --breakpoint-tablet-lg: 768px;
  --shadow-standard: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
}
</file>

<file path="playwright.config.ts">
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './playwright/tests',
  fullyParallel: true,

  // Prevent accidental test.only commits in CI
  forbidOnly: !!process.env.CI,

  retries: process.env.CI ? 2 : 0,

  workers: process.env.CI ? 1 : undefined,

  reporter: process.env.CI
    ? [
        [
          'junit',
          {
            outputFile:
              process.env.PLAYWRIGHT_JUNIT_OUTPUT_NAME ||
              'test-results/playwright/results.xml',
          },
        ],
        ['html'],
      ]
    : 'html',

  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    // viewport for HHT devices
    viewport: { width: 360, height: 545 },
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  webServer: {
    command: process.env.CI ? 'npm run build && npm run start' : 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 180 * 1000,
  },
});
</file>

<file path=".gitlab-ci.yml">
variables:
  NODE_VERSION: '22'

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      variables:
        runBuild: true
        PUBLISH_COVERAGE: 'true'
        # Use a path prefix for MR's so multiple coverage reports can be published at once
        COVERAGE_PATH_PREFIX: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - if: $CI_COMMIT_BRANCH == "main"
      variables:
        PUBLISH_COVERAGE: 'true'
    - if: $CI_COMMIT_BRANCH =~ /user/
      variables:
        runBuild: true
    - if: $CI_PIPELINE_SOURCE == 'web'
      variables:
        runBuild: false

stages:
  - test
  - build
  - deploy

unit-tests:
  stage: test
  image: 'node:${NODE_VERSION}'
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
  script:
    - npm install
    - npm run test:coverage
    # correct value of <source> element in cobertura report to allow Gitlab to recognise coverage
    - sed -i "s#<source>/builds/coopeng/retail/stores/store-hub/store-hub-app</source>#<source></source>#g" coverage/cobertura-coverage.xml
  coverage: /Statements\s+:\s+([\d\.]+)/
  artifacts:
    when: always
    paths:
      - test-results/**/*.xml
      - coverage/
    reports:
      junit: test-results/**/*.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    expire_in: 1 week

e2e-tests:
  stage: test
  image: mcr.microsoft.com/playwright:v1.52.0-noble
  variables:
    NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED: $NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/

  script:
    - npm install
    - npm run setup-e2e
    - npm run test:e2e
  artifacts:
    when: always
    reports:
      junit: test-results/playwright/results.xml
    paths:
      - playwright-report/
      - test-results/
    expire_in: 1 week

build-job:
  stage: build
  image: 'node:${NODE_VERSION}'
  variables:
    NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED: $NEXT_PUBLIC_HOW_DOI_SEARCH_ENABLED
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
  rules:
    - if: $runBuild
  script:
    - npm install
    - npm run build

publish-coverage:
  stage: deploy
  dependencies:
    - unit-tests
  rules:
    - if: $PUBLISH_COVERAGE
  script:
    - mv coverage/ public/
  pages:
    path_prefix: $COVERAGE_PATH_PREFIX
    expire_in: 14 days
</file>

</files>
