import { Dayjs } from 'dayjs';

type Week = {
  week: number,
  year: number
}

/**
 * Get the Co-op week number and year for a given date
 * @param date Date to get week number for, as a Day.js date
 */
export const getWeekNumber = (date: Dayjs): Week => {
  // find start of Co-op year - months are 0-indexed so 0 is January
  const firstJan = date.month(0).date(1);
  const firstSunday = firstJan.day() === 0
    ? firstJan // 1st Jan is a Sunday, so this is the start of the Co-op year
    : firstJan.add(7 - firstJan.day(), 'd'); // add days to get to the next Sunday

  if (date.isBefore(firstSunday)) {
    // before start of Co-op year, so falls in previous Co-op year
    // find week for last Sunday to get the week number since it is guaranteed to be in the last year
    return getWeekNumber(date.add(-date.day(), 'd'));
  }

  const daysSinceFirstSunday = date.diff(firstSunday, 'd');
  return {
    week: Math.floor(daysSinceFirstSunday / 7.0) + 1,
    year: date.year()
  };
};