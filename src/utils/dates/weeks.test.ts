import dayjs from 'dayjs';
import { getWeekNumber } from "@/utils/dates/weeks";

describe('getWeekNumber', () => {
  it('returns week 1 for 1st Jan when 1st Jan is a Sunday', () => {
    const week = getWeekNumber(dayjs('2023-01-01'));

    expect(week).toMatchObject({ week: 1, year: 2023 });
  });

  it('returns last week of previous year for 1st Jan when 1st Jan is not a Sunday', () => {
    const week = getWeekNumber(dayjs('2025-01-01'));

    expect(week.week).toBe(52);
    expect(week.year).toBe(2024);
  });

  it('returns week 1 for first Sunday of year', () => {
    const week = getWeekNumber(dayjs('2025-01-05'));

    expect(week).toMatchObject({ week: 1, year: 2025 });
  });

  it('returns same week for all days of week', () => {
    const startOfWeek = dayjs('2025-06-22');

    for (let i = 0; i < 6; i++) {
      const week = getWeekNumber(startOfWeek.add(i, 'day'));

      expect(week).toMatchObject({ week: 25, year: 2025 });
    }
  });

  it('returns week 52 for 31st Dec for years with 52 weeks', () => {
    const week = getWeekNumber(dayjs('2025-12-31'));

    expect(week).toMatchObject({ week: 52, year: 2025 });
  });

  it('returns week 53 for 31st Dec for years with 53 weeks', () => {
    const week = getWeekNumber(dayjs('2023-12-31'));

    expect(week).toMatchObject({ week: 53, year: 2023 });
  });
});