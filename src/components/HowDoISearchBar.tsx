'use client';

import React, { useState, FormEvent } from 'react';
import { tv, type VariantProps } from 'tailwind-variants';
import { launchHowDoIApp } from '@/utils/launchHowDoIApp';
import { SearchIcon } from '@/components/icons/UIIcons';

const searchBar = tv({
  slots: {
    container: 'w-full flex items-center justify-center',
    form: 'flex items-center w-[328px] sm:w-[500px] md:w-[600px] h-[44px] rounded-[4px] shadow-standard overflow-hidden border',
    input:
      'flex-grow text-base font-normal bg-transparent outline-none px-3 py-2',
    button:
      'w-[44px] h-[44px] flex items-center justify-center flex-shrink-0 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-r-[4px] cursor-pointer',
    icon: 'w-5 h-5',
  },
  variants: {
    enabled: {
      true: {
        container: 'block',
        form: 'border-border-primary bg-white',
        input: 'text-text-primary text-base placeholder:text-border-primary',
        button: 'bg-primary hover:bg-primary-hover focus:ring-primary',
      },
      false: {
        container: 'hidden',
      },
    },
    submitting: {
      true: {
        button: 'opacity-50 cursor-not-allowed',
        input: 'opacity-50',
      },
      false: {},
    },
  },
  defaultVariants: {
    enabled: true,
    submitting: false,
  },
});

interface HowDoISearchBarProps extends VariantProps<typeof searchBar> {
  isFeatureEnabled?: boolean;
  placeholder?: string;
}

export function HowDoISearchBar({
  enabled = true,
  isFeatureEnabled = true,
  placeholder = 'Ask How Do I...',
}: HowDoISearchBarProps) {
  const [searchText, setSearchText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const shouldShow = isFeatureEnabled && enabled;

  const { container, form, input, button, icon } = searchBar({
    enabled: shouldShow,
    submitting: isSubmitting,
  });

  const handleSubmit = (event: FormEvent) => {
    event.preventDefault();

    if (!searchText.trim() || isSubmitting) {
      return;
    }

    setIsSubmitting(true);

    try {
      launchHowDoIApp(searchText.trim());
      setSearchText('');
    } catch (error) {
      console.error('Failed to launch HowDoI app:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={container()}>
      <form
        onSubmit={handleSubmit}
        className={form()}
        role="search"
        aria-label="How Do I search"
      >
        <input
          type="text"
          placeholder={placeholder}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className={input()}
          aria-label="Ask How Do I question"
          disabled={isSubmitting}
          autoComplete="off"
          spellCheck="false"
        />
        <button
          type="submit"
          className={button()}
          aria-label="Search"
          disabled={!searchText.trim() || isSubmitting}
        >
          <SearchIcon className={icon()} size={24} />
        </button>
      </form>
    </div>
  );
}
