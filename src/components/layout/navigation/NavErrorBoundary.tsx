'use client';

import React, { Component, ReactNode } from 'react';

interface NavErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface NavErrorBoundaryProps {
  children: ReactNode;
}

function NavFallback({ onRetry }: { onRetry: () => void }) {
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 h-20 bg-red-50 border-t border-red-200 flex items-center justify-center">
      <div className="text-center">
        <p className="text-sm text-red-600 mb-2">
          Navigation temporarily unavailable
        </p>
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 transition-colors"
          aria-label="Retry navigation"
        >
          Try Again
        </button>
      </div>
    </div>
  );
}

export class NavErrorBoundary extends Component<
  NavErrorBoundaryProps,
  NavErrorBoundaryState
> {
  constructor(props: NavErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): NavErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Navigation Error:', error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      return <NavFallback onRetry={this.handleRetry} />;
    }

    return this.props.children;
  }
}
