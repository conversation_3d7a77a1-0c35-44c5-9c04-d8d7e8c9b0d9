import Link from 'next/link';
import { tv, type VariantProps } from 'tailwind-variants';
import type { NavItem } from '@/config/navigation';

const tab = tv({
  slots: {
    link: 'flex flex-col items-center justify-center rounded-lg p-2 text-xs text-gray-600 no-underline transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2',
    iconWrapper: 'rounded-full py-1 transition-all duration-200',
    icon: 'h-7 w-7 transition-colors',
    label: 'text-xs font-medium',
  },
  variants: {
    isSelected: {
      true: {
        link: 'text-gray-900',
        iconWrapper: 'bg-slate-200 px-16',
        icon: 'text-gray-900',
        label: 'font-semibold text-gray-900 pt-1',
      },
      false: {
        link: 'text-gray-600 hover:bg-gray-100 hover:text-gray-800',
        iconWrapper: 'bg-transparent px-4',
        icon: 'text-gray-600',
        label: 'text-gray-600 pt-1',
      },
    },
    size: {
      default: {
        icon: 'h-8 w-8',
      },
      large: {
        icon: 'h-10 w-10',
      },
      small: {
        icon: 'h-6 w-6',
      },
    },
  },
  defaultVariants: {
    size: 'default',
  },
});

interface NavTabProps extends VariantProps<typeof tab> {
  item: NavItem;
  isSelected: boolean;
  iconClassName?: string;
}

export function NavTab({ item, isSelected, size, iconClassName }: NavTabProps) {
  const { link, iconWrapper, icon, label } = tab({ isSelected, size });
  const renderIcon = () => {
    const IconComponent = item.icon;
    return (
      <div className="flex w-24 justify-center">
        <div className={iconWrapper()}>
          <IconComponent
            className={`${icon()} ${iconClassName || ''}`}
            width={32}
            height={32}
            isSelected={isSelected}
            aria-hidden="true"
          />
        </div>
      </div>
    );
  };

  return (
    <Link
      href={item.href}
      className={link()}
      aria-current={isSelected ? 'page' : undefined}
      aria-label={`Maps to ${item.label}`}
    >
      {renderIcon()}
      <span className={label()}>{item.label}</span>
    </Link>
  );
}
