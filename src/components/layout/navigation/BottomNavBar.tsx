'use client';

import { useNavigation } from '@/hooks/useNavigation';
import { NavTab } from '@/components/layout/navigation/NavTab';
import { navItems } from '@/config/navigation';

export function BottomNavBar() {
  const { isTabActive } = useNavigation();

  const navClass =
    'flex h-full w-full items-center justify-evenly px-4';

  return (
    <header className="fixed bottom-0 left-0 right-0 z-50 h-20 border-t border-gray-200 bg-white shadow-nav-bar">
      <nav role="navigation" aria-label="Main navigation" className={navClass}>
        {navItems.map((item) => (
          <NavTab
            key={item.href}
            item={item}
            isSelected={isTabActive(item.href)}
            iconClassName="h-6 w-6 sm:h-8 sm:w-8"
          />
        ))}
      </nav>
    </header>
  );
}
