import { render, screen } from '@testing-library/react';
import { BottomNavBar } from '@/components/layout/navigation/BottomNavBar';

jest.mock('next/navigation', () => ({
  usePathname: jest.fn(() => '/'),
}));

jest.mock('@/hooks/useNavigation', () => ({
  useNavigation: jest.fn(() => ({
    isTabActive: jest.fn((href: string) => href === '/'),
  })),
}));

jest.mock('@/config/navigation', () => ({
  navItems: [
    {
      href: '/',
      label: 'Home',
      icon: () => <div data-testid="home-icon">Home Icon</div>,
    },
    {
      href: '/apps',
      label: 'Apps',
      icon: () => <div data-testid="apps-icon">Apps Icon</div>,
    },
  ],
}));

describe('BottomNavBar', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the mobile navigation', () => {
    render(<BottomNavBar />);

    const nav = screen.getByRole('navigation');
    expect(nav).toBeInTheDocument();
    expect(nav).toHaveAttribute('aria-label', 'Main navigation');

    expect(screen.getByLabelText('Maps to Home')).toBeInTheDocument();
    expect(screen.getByLabelText('Maps to Apps')).toBeInTheDocument();
  });

  it('should correctly identify the active tab', () => {
    render(<BottomNavBar />);

    expect(screen.getByLabelText('Maps to Home')).toHaveAttribute(
      'aria-current',
      'page',
    );
    expect(screen.getByLabelText('Maps to Apps')).not.toHaveAttribute(
      'aria-current',
      'page',
    );
  });
});
