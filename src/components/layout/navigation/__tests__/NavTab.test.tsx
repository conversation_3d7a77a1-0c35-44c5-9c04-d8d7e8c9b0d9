import { render, screen } from '@testing-library/react';
import { NavTab } from '@/components/layout/navigation/NavTab';
import type { NavItem } from '@/config/navigation';
import '@testing-library/jest-dom';

const mockHomeItem: NavItem = {
  href: '/',
  label: 'Home',
  icon: ({ className }) => (
    <svg className={className} data-testid="home-icon" />
  ),
};

describe('NavTab', () => {
  it('should render with selected styling including background', () => {
    render(<NavTab item={mockHomeItem} isSelected={true} />);

    const link = screen.getByRole('link', { name: /maps to home/i });
    expect(link).toBeInTheDocument();

    const iconWrapper = link.querySelector('div[class*="bg-slate-200"]');
    expect(iconWrapper).toBeInTheDocument();
  });

  it('should render without selected styling when not selected', () => {
    render(<NavTab item={mockHomeItem} isSelected={false} />);
    const link = screen.getByRole('link', { name: /maps to home/i });
    expect(link).toBeInTheDocument();

    const iconWrapper = link.querySelector('div[class*="bg-slate-200"]');
    expect(iconWrapper).not.toBeInTheDocument();
  });
});
