import { HeaderBar } from '@/components/layout/header/HeaderBar';
import { render } from '@testing-library/react';

describe('HeaderBar', () => {
  it('renders the header component with children', () => {
    const mockChildren = (<div data-testid='mock-test-id'>Mock Content</div>);
    const { getByTestId } = render(<HeaderBar>{mockChildren}</HeaderBar>);
    expect(getByTestId('mock-test-id')).toBeInTheDocument();
  });
});