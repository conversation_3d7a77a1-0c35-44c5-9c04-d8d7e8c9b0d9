import { HowDoISearchBar } from '@/components/HowDoISearchBar';

interface HomePageProps {
  howDoISearchEnabled?: boolean;
}

export function HomePage({ howDoISearchEnabled = true }: HomePageProps) {
  return (
    <div className="w-screen h-screen flex flex-col justify-end pb-28">
      <div className="flex justify-center px-4">
        <div className="w-full max-w-[600px]">
          <HowDoISearchBar isFeatureEnabled={howDoISearchEnabled} />
        </div>
      </div>
    </div>
  );
}
