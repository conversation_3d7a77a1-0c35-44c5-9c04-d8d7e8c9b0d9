import { forwardRef } from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

const categoryPill = tv({
  slots: {
    button:
      'cursor-pointer flex items-center justify-center rounded-full px-4 py-2 text-sm font-medium transition-[background-color,box-shadow,transform] duration-200 ease-out focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 whitespace-nowrap h-[40px] max-h-[40px] touch-manipulation hover:scale-[1.02] active:scale-[0.98]',
    text: 'transition-colors duration-200 ease-out',
  },
  variants: {
    isSelected: {
      true: {
        button: 'bg-background-black text-white shadow-standard',
        text: 'text-white font-semibold',
      },
      false: {
        button:
          'bg-white text-gray-700 shadow-standard hover:bg-gray-50 hover:text-gray-900',
        text: 'text-gray-700',
      },
    },
    size: {
      default: {
        button: 'h-[40px] max-h-[40px] px-[15px] py-[5px]',
        text: 'hht:text-sm tablet:text-sm tablet-lg:text-base',
      },
      small: {
        button: 'px-3 py-2 min-h-[40px]',
        text: 'text-xs',
      },
      large: {
        button: 'px-6 py-4 min-h-[48px]',
        text: 'text-base',
      },
    },
  },
  defaultVariants: {
    size: 'default',
  },
});

interface CategoryPillProps extends VariantProps<typeof categoryPill> {
  category: string;
  isSelected: boolean;
  onClick: (category: string) => void;
  className?: string;
  tabIndex?: number;
  onKeyDown?: (event: React.KeyboardEvent<HTMLButtonElement>) => void;
}

export const CategoryPill = forwardRef<HTMLButtonElement, CategoryPillProps>(
  (
    {
      category,
      isSelected,
      onClick,
      size,
      className,
      tabIndex = -1,
      onKeyDown,
      ...props
    },
    ref,
  ) => {
    const { button, text } = categoryPill({ isSelected, size });

    return (
      <button
        ref={ref}
        type="button"
        className={`${button()} ${className || ''}`}
        onClick={() => onClick(category)}
        onKeyDown={onKeyDown}
        role="tab"
        aria-selected={isSelected}
        aria-label={category}
        tabIndex={tabIndex}
        style={{ scrollSnapAlign: 'start' }}
        {...props}
      >
        <span className={text()}>{category}</span>
      </button>
    );
  },
);

CategoryPill.displayName = 'CategoryPill';
