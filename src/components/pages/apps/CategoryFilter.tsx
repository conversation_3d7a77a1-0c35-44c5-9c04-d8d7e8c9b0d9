'use client';

import { useCallback } from 'react';
import { tv } from 'tailwind-variants';
import { CategoryPill } from './CategoryPill';

const categoryFilterVariants = tv({
  slots: {
    wrapper:
      "pt-4 transition-all duration-200 ease-in-out",
    scrollContainer:
      'pt-1 pb-5 px-4 hht:px-4 tablet:px-6 leading-none flex gap-2 hht:gap-3 tablet:gap-4 overflow-x-auto scrollbar-hide transition-all duration-150 ease-in-out snap-x snap-mandatory scroll-px-3',
  },
});

interface CategoryFilterProps {
  categories: string[];
  selectedCategory: string;
  onCategorySelect: (category: string) => void;
  className?: string;
}

export function CategoryFilter({
  categories,
  selectedCategory,
  onCategorySelect,
  className,
}: CategoryFilterProps) {
  const handleCategorySelect = useCallback(
    (category: string) => {
      onCategorySelect(category);
    },
    [onCategorySelect],
  );

  const { wrapper, scrollContainer } = categoryFilterVariants();

  return (
    <div
      className={`${wrapper()} ${className || ''}`}
    >
      <div
        role="tablist"
        aria-label="Filter apps by category"
      >
        <div
          className={scrollContainer()}
          style={{
            WebkitOverflowScrolling: 'touch',
          }}
        >
          {categories.map((category) => (
            <CategoryPill
              key={category}
              category={category}
              isSelected={selectedCategory === category}
              onClick={handleCategorySelect}
              className="flex-shrink-0"
            />
          ))}
        </div>
      </div>
    </div>
  );
}
