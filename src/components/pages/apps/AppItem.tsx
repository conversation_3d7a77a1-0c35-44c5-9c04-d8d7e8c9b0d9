import Image from "next/image";

// TODO: Move this type somewhere else.
// For now it is ok in here but this type would be used for the list of apps coming getting posted into iframe.
export type AppItem = {
  appName: string,
  appLaunchUrl: string,
  appIcon: string,
  category?: string
}

export function AppItem({appItem}: {appItem: AppItem}) {
  return (
    <div role="button" data-testid={`app-item-${appItem.appName}`} className="w-full h-[86px] flex items-center pr-4 gap-4 bg-white rounded-[24px] shadow-standard">
      {/* This will likely be a base64 encoded image and you can't use the Image component to render those hence the eslint disable */}
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img src={appItem.appIcon} alt={`${appItem.appName} Icon`} className="w-[75px] h-[75px]" />
      <span className="text-base font-medium text-grey-900">{appItem.appName}</span>
      <Image
        src="/icons/icon-arrow-right.svg"
        alt="arrow icon"
        width={24}
        height={24}
        className="ml-auto"
      />
    </div>
  )
}