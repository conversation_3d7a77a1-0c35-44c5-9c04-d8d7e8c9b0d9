import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CategoryFilter } from '../CategoryFilter';
import { CategoryPill } from '../CategoryPill';

// Sample categories for tests
export const DEFAULT_CATEGORIES = [
  'All apps',
  'Analytics',
  'Customer service',
  'Operations',
  'Stock management',
  'Workforce',
];

// CategoryFilter test helpers
export const categoryFilterUtils = {
  render: (overrideProps = {}) => {
    const mockOnCategorySelect = jest.fn();
    const defaultProps = {
      categories: DEFAULT_CATEGORIES,
      selectedCategory: 'All apps',
      onCategorySelect: mockOnCategorySelect,
    };
    const props = { ...defaultProps, ...overrideProps };
    const renderResult = render(<CategoryFilter {...props} />);
    return {
      renderResult,
      mockOnCategorySelect,
    };
  },

  getTabByCategory: (categoryName: string) =>
    screen.getByRole('tab', { name: categoryName }),

  expectCategorySelected: (categoryName: string) => {
    const tab = categoryFilterUtils.getTabByCategory(categoryName);
    expect(tab).toHaveAttribute('aria-selected', 'true');
  },

  expectCategoryNotSelected: (categoryName: string) => {
    const tab = categoryFilterUtils.getTabByCategory(categoryName);
    expect(tab).toHaveAttribute('aria-selected', 'false');
  },

  async simulateKeyboardNavigation(
    user: ReturnType<typeof userEvent.setup>,
    keys: string[],
  ) {
    for (const key of keys) {
      await user.keyboard(`{${key}}`);
    }
  },

  async clickCategory(
    user: ReturnType<typeof userEvent.setup>,
    categoryName: string,
  ) {
    const tab = categoryFilterUtils.getTabByCategory(categoryName);
    await user.click(tab);
  },
};

// CategoryPill test helpers
export const categoryPillUtils = {
  render: (overrideProps = {}) => {
    const mockOnClick = jest.fn();
    const defaultProps = {
      category: 'Test Category',
      isSelected: false,
      onClick: mockOnClick,
    };
    const props = { ...defaultProps, ...overrideProps };
    const renderResult = render(
      <div role="tablist">
        <CategoryPill {...props} />
      </div>,
    );
    return {
      renderResult,
      mockOnClick,
    };
  },

  getTab: () => screen.getByRole('tab'),

  expectAriaLabel: (categoryName: string) => {
    expect(screen.getByLabelText(categoryName)).toBeInTheDocument();
  },

  async clickTab(user: ReturnType<typeof userEvent.setup>) {
    const tab = categoryPillUtils.getTab();
    await user.click(tab);
  },
};

export const testPatterns = {
  setupUser: () => userEvent.setup(),

  expectTabCount: (count: number) => {
    expect(screen.getAllByRole('tab')).toHaveLength(count);
  },

  expectTabList: () => {
    expect(
      screen.getByRole('tablist', { name: 'Filter apps by category' }),
    ).toBeInTheDocument();
  },
};

export const performanceUtils = {
  measureTestTime: (testName: string) => {
    const start = performance.now();
    return {
      end: () => {
        const duration = performance.now() - start;
        if (duration > 100) {
          // Warn when test exceeds 100ms
          console.warn(`Test "${testName}" took ${duration.toFixed(2)}ms`);
        }
        return duration;
      },
    };
  },
};
