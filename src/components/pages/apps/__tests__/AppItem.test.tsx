import { AppItem } from '@/components/pages/apps/AppItem';
import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('AppItem', () => {
  const mockAppItem: AppItem = {
    appName: 'Test App',
    appIcon: '/icons/test-icon.png',
    appLaunchUrl: 'launch://testapp', // Currently unused
  };

  it('renders app item correctly', () => {
    render(<AppItem appItem={mockAppItem} />);
    expect(screen.getByText(mockAppItem.appName)).toBeInTheDocument();
    expect(screen.getByAltText('Test App Icon')).toHaveAttribute(
      'src',
      mockAppItem.appIcon,
    );
  });

  it('should have no accessibility violations', async () => {
    const { container } = render(<AppItem appItem={mockAppItem} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
