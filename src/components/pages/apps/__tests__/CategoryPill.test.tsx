import { axe, toHaveNoViolations } from 'jest-axe';
import { categoryPillUtils, testPatterns } from './test-utils';

expect.extend(toHaveNoViolations);

describe('CategoryPill', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Core Functionality', () => {
    it('has correct aria-label for accessibility', () => {
      categoryPillUtils.render({ category: 'Test Category' });
      categoryPillUtils.expectAriaLabel('Test Category');
    });

    it('calls onClick with category when clicked', async () => {
      const user = testPatterns.setupUser();
      const { mockOnClick } = categoryPillUtils.render({
        category: 'Test Category',
      });

      await categoryPillUtils.clickTab(user);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
      expect(mockOnClick).toHaveBeenCalledWith('Test Category');
    });

    it('should have no accessibility violations', async () => {
      const {
        renderResult: { container },
      } = categoryPillUtils.render();
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });
});
