import { render, screen } from '@testing-library/react';
import { AppsPage } from '@/components/pages/apps/AppsPage';

describe('AppsPage', () => {
  it('renders the apps page', () => {
    const { container } = render(<AppsPage />);
    expect(container).toBeInTheDocument();
  });

  it('renders the header', () => {
    render(<AppsPage />);
    expect(screen.getByText('Apps'))
  });

  it('renders app items', () => {
    render(<AppsPage/>);
    const appItems = screen.getAllByTestId(/app-item-/);
    expect(appItems.length).toBeGreaterThan(0);
  });
});