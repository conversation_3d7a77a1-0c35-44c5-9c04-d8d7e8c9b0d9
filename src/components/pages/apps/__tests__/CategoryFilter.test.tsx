import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { CategoryFilter } from '../CategoryFilter';
import {
  categoryFilterUtils,
  testPatterns,
  DEFAULT_CATEGORIES,
} from './test-utils';

expect.extend(toHaveNoViolations);

describe('CategoryFilter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Category Selection', () => {
    it('calls onCategorySelect when a category is clicked', async () => {
      const user = testPatterns.setupUser();
      const { mockOnCategorySelect } = categoryFilterUtils.render();

      await categoryFilterUtils.clickCategory(user, 'Workforce');

      expect(mockOnCategorySelect).toHaveBeenCalledTimes(1);
      expect(mockOnCategorySelect).toHaveBeenCalledWith('Workforce');
    });
  });

  describe('Accessibility Integration', () => {
    it('maintains basic accessibility compliance and ARIA state management', async () => {
      const user = userEvent.setup();

      const { container, rerender } = render(
        <CategoryFilter
          categories={DEFAULT_CATEGORIES}
          selectedCategory="All apps"
          onCategorySelect={jest.fn()}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();

      const allAppsTab = categoryFilterUtils.getTabByCategory('All apps');
      const workforceTab = categoryFilterUtils.getTabByCategory('Workforce');

      expect(allAppsTab).toHaveAttribute('aria-selected', 'true');
      expect(workforceTab).toHaveAttribute('aria-selected', 'false');

      const mockOnCategorySelect = jest.fn();
      await user.click(workforceTab);

      rerender(
        <CategoryFilter
          categories={DEFAULT_CATEGORIES}
          selectedCategory="Workforce"
          onCategorySelect={mockOnCategorySelect}
        />,
      );

      expect(workforceTab).toHaveAttribute('aria-selected', 'true');
      expect(allAppsTab).toHaveAttribute('aria-selected', 'false');

      const finalResults = await axe(container);
      expect(finalResults).toHaveNoViolations();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty categories array', () => {
      categoryFilterUtils.render({ categories: [] });
      testPatterns.expectTabList();
      expect(screen.queryByRole('tab')).not.toBeInTheDocument();
    });

    it('handles invalid selected category', () => {
      categoryFilterUtils.render({ selectedCategory: 'Non-existent' });
      testPatterns.expectTabCount(6);
    });
  });
});
