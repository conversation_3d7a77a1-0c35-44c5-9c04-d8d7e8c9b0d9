import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { CategoryFilter } from '../CategoryFilter';
import {
  categoryFilterUtils,
  testPatterns,
  DEFAULT_CATEGORIES,
} from '../test-utils';

expect.extend(toHaveNoViolations);

describe('CategoryFilter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Category Selection', () => {
    it('calls onCategorySelect when a category is clicked', async () => {
      const user = testPatterns.setupUser();
      const { mockOnCategorySelect } = categoryFilterUtils.render();

      await categoryFilterUtils.clickCategory(user, 'Workforce');

      expect(mockOnCategorySelect).toHaveBeenCalledTimes(1);
      expect(mockOnCategorySelect).toHaveBeenCalledWith('Workforce');
    });
  });

  describe('Keyboard Navigation - Essential Behavior', () => {
    it('should handle arrow navigation with wrapping', async () => {
      const user = testPatterns.setupUser();
      categoryFilterUtils.render();

      // Test wrap-around navigation
      const allAppsTab = categoryFilterUtils.getTabByCategory('All apps');
      const lastTab = categoryFilterUtils.getTabByCategory('Workforce');

      allAppsTab.focus();

      // ArrowLeft wraps to last tab
      await user.keyboard('{ArrowLeft}');
      expect(lastTab).toHaveFocus();

      // ArrowRight wraps to first tab
      await user.keyboard('{ArrowRight}');
      expect(allAppsTab).toHaveFocus();
    });

    it('should handle jump navigation and activation', async () => {
      const user = testPatterns.setupUser();
      const { mockOnCategorySelect } = categoryFilterUtils.render();

      // Move focus to second tab
      const allAppsTab = categoryFilterUtils.getTabByCategory('All apps');
      allAppsTab.focus();

      await user.keyboard('{ArrowRight}');

      // Enter key activates tab
      await user.keyboard('{Enter}');
      expect(mockOnCategorySelect).toHaveBeenCalledWith('Analytics');

      // Space key activates tab
      mockOnCategorySelect.mockClear();
      await user.keyboard('{ArrowRight}');
      await user.keyboard(' ');
      expect(mockOnCategorySelect).toHaveBeenCalledWith('Customer service');
    });
  });

  describe('Accessibility Integration', () => {
    it('maintains basic accessibility compliance and ARIA state management', async () => {
      const user = userEvent.setup();

      // Basic accessibility integration test
      const { container, rerender } = render(
        <CategoryFilter
          categories={DEFAULT_CATEGORIES}
          selectedCategory="All apps"
          onCategorySelect={jest.fn()}
        />,
      );

      // Check no accessibility violations
      const results = await axe(container);
      expect(results).toHaveNoViolations();

      // Check ARIA state during transitions
      const allAppsTab = categoryFilterUtils.getTabByCategory('All apps');
      const workforceTab = categoryFilterUtils.getTabByCategory('Workforce');

      // Initial selected state
      expect(allAppsTab).toHaveAttribute('aria-selected', 'true');
      expect(allAppsTab).toHaveAttribute('tabIndex', '0');
      expect(workforceTab).toHaveAttribute('aria-selected', 'false');
      expect(workforceTab).toHaveAttribute('tabIndex', '-1');

      // Simulate user interaction
      const mockOnCategorySelect = jest.fn();
      await user.click(workforceTab);

      // Rerender with updated selection
      rerender(
        <CategoryFilter
          categories={DEFAULT_CATEGORIES}
          selectedCategory="Workforce"
          onCategorySelect={mockOnCategorySelect}
        />,
      );

      // Confirm ARIA updates after rerender
      expect(workforceTab).toHaveAttribute('aria-selected', 'true');
      expect(workforceTab).toHaveAttribute('tabIndex', '0');
      expect(allAppsTab).toHaveAttribute('aria-selected', 'false');
      expect(allAppsTab).toHaveAttribute('tabIndex', '-1');

      // Check accessibility after updates
      const finalResults = await axe(container);
      expect(finalResults).toHaveNoViolations();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty categories array', () => {
      categoryFilterUtils.render({ categories: [] });
      testPatterns.expectTabList();
      expect(screen.queryByRole('tab')).not.toBeInTheDocument();
    });

    it('handles invalid selected category', () => {
      categoryFilterUtils.render({ selectedCategory: 'Non-existent' });
      testPatterns.expectTabCount(6);
    });
  });
});
