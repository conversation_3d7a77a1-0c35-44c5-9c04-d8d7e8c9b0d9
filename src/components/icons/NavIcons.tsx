import React from 'react';

type IconProps = {
  className?: string;
  size?: number;
  isSelected?: boolean;
  title?: string;
};

export const HomeIcon = ({
  className,
  isSelected,
  size = 28,
  title = 'Home Icon',
}: IconProps) => (
  <div className={className} style={{ width: size, height: size }}>
    <svg
      width="100%"
      height="100%"
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 26 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>{title}</title>
      <path
        d="M2.64624 10.485L12.8001 0.990479L22.9539 10.485V23.1443H2.64624V10.485Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <path
        d="M24.8 11.9326L12.8 0.855713"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M0.800048 11.9326L12.8 0.855713"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.8 14.0002H18.8V23.0002H12.8V14.0002Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
        fill={isSelected ? '#DDE9ED' : 'none'}
      />
    </svg>
  </div>
);

export const AppsIcon = ({
  className,
  isSelected,
  size = 30,
  title = 'Apps Icon',
}: IconProps) => (
  <div className={className} style={{ width: size, height: size }}>
    <svg
      width="100%"
      height="100%"
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>{title}</title>
      <rect
        x="6"
        y="6"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <rect
        x="18"
        y="6"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <rect
        x="6"
        y="18"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={isSelected ? 'currentColor' : 'none'}
      />
      <rect
        x="18"
        y="18"
        width="8"
        height="8"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        fill={!isSelected ? 'currentColor' : 'none'}
      />
    </svg>
  </div>
);
