import React from 'react';

type IconProps = {
  className?: string;
  size?: number;
  title?: string;
};

export const SearchIcon = ({
  className,
  size = 24,
  title = 'Search Icon',
}: IconProps) => (
  <div className={className} style={{ width: size, height: size }}>
    <svg
      width="100%"
      height="100%"
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>{title}</title>
      <path
        d="M22.7836 21.6086L16.9988 15.8237C18.3546 14.1967 19.0777 12.2082 19.0777 10.0388C19.0777 5.06748 15.0102 1 10.0388 1C5.06748 1 1 5.06748 1 10.0388C1 15.0102 5.06748 19.0777 10.0388 19.0777C12.1178 19.0777 14.1967 18.3546 15.8237 16.9988L21.6086 22.7836C21.7893 22.9644 21.9701 23.0548 22.2413 23.0548C22.5125 23.0548 22.6932 22.9644 22.874 22.7836C23.0548 22.4221 23.0548 21.8797 22.7836 21.6086ZM17.3603 10.0388C17.3603 14.1063 14.1063 17.3603 10.0388 17.3603C5.97137 17.3603 2.71738 14.1063 2.71738 10.0388C2.71738 5.97137 5.97137 2.71738 10.0388 2.71738C14.0159 2.71738 17.3603 5.97137 17.3603 10.0388Z"
        fill="white"
      />
    </svg>
  </div>
);
