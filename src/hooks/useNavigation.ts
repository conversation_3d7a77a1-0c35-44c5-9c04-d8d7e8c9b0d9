'use client';

import { useCallback } from 'react';
import { usePathname } from 'next/navigation';

export function useNavigation() {
  const pathname = usePathname();

  const isTabActive = useCallback(
    (href: string) => {
      if (href === '/') {
        return pathname === '/';
      }
      return pathname.startsWith(href);
    },
    [pathname],
  );

  return {
    pathname,
    isTabActive,
  };
}
