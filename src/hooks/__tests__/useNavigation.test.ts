import { renderHook } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { useNavigation } from '../useNavigation';

jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('useNavigation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('isTabActive', () => {
    it('should return true for the root path when href is "/"', () => {
      mockUsePathname.mockReturnValue('/');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/')).toBe(true);
    });

    it('should return false for a nested path when href is "/"', () => {
      mockUsePathname.mockReturnValue('/some-page');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/')).toBe(false);
    });

    it('should return true when current path starts with tab href', () => {
      mockUsePathname.mockReturnValue('/apps');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(true);
    });

    it('should return true for nested paths of a non-root href', () => {
      mockUsePathname.mockReturnValue('/apps/some-app');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(true);
    });

    it('should return false when current path does not match', () => {
      mockUsePathname.mockReturnValue('/other');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(false);
    });

    it('should not activate non-root tabs for the root path', () => {
      mockUsePathname.mockReturnValue('/');
      const { result } = renderHook(() => useNavigation());
      expect(result.current.isTabActive('/apps')).toBe(false);
    });
  });

  describe('pathname', () => {
    it('should return current pathname', () => {
      const testPath = '/current/path';
      mockUsePathname.mockReturnValue(testPath);

      const { result } = renderHook(() => useNavigation());

      expect(result.current.pathname).toBe(testPath);
    });
  });
});
