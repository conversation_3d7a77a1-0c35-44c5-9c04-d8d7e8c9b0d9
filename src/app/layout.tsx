import type { Metadata, Viewport } from 'next';
import { BottomNavBar } from '@/components/layout/navigation/BottomNavBar';
import { NavErrorBoundary } from '@/components/layout/navigation/NavErrorBoundary';
import './globals.css';

export const metadata: Metadata = {
  title: 'Store Hub',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body
        className="antialiased h-full flex flex-col bg-background-blue text-foreground-light"
      >
        <main className="flex flex-grow flex-col h-full overflow-y-hidden">
          {children}
        </main>
        <NavErrorBoundary>
          <BottomNavBar />
        </NavErrorBoundary>
      </body>
    </html>
  );
}
