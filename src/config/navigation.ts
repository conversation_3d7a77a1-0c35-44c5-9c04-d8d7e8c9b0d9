export type NavItem = {
  href: string;
  label: string;
  icon: React.FC<{
    width?: number;
    height?: number;
    className?: string;
    isSelected?: boolean;
  }>;
};

import { AppsIcon, HomeIcon } from '@/components/icons/NavIcons';

export const navItems: NavItem[] = [
  {
    href: '/',
    label: 'Home',
    icon: HomeIcon,
  },
  {
    href: '/apps',
    label: 'Apps',
    icon: AppsIcon,
  },
];
