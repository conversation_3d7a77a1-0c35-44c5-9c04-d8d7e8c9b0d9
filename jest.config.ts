import type { Config } from 'jest';
import nextJest from 'next/jest.js';

const createJestConfig = nextJest({
  dir: './',
});

const config: Config = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  testPathIgnorePatterns: ['<rootDir>/playwright/', 'test-utils.tsx'], // Exclude Playwright end-to-end tests

  maxWorkers: process.env.CI ? '50%' : '75%',
  clearMocks: true,
  resetMocks: false, // Do not reset mocks to improve performance
  restoreMocks: false, // Do not restore mocks to improve performance
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',

  testSequencer: '@jest/test-sequencer',

  reporters: ['default', ['jest-junit', { outputDirectory: 'test-results' }]],
  coverageReporters: ['cobertura', 'text-summary', 'html'],
  collectCoverageFrom: [
    '<rootDir>/src/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/*test-utils*',
  ],
  coverageDirectory: 'coverage',

  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },

  transformIgnorePatterns: ['node_modules/(?!(.*\\.mjs$))'],
};

export default createJestConfig(config);
