import type { Config } from 'jest';
import nextJest from 'next/jest.js';

const createJestConfig = nextJest({
  dir: './',
});

const config: Config = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  testPathIgnorePatterns: ['<rootDir>/playwright/'], // Exclude Playwright E2E tests

  maxWorkers: process.env.CI ? '50%' : '75%',
  clearMocks: true,
  resetMocks: false, // Avoid overhead by not resetting mocks
  restoreMocks: false, // Avoid overhead by not restoring mocks
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',

  testSequencer: '@jest/test-sequencer',

  reporters: ['default', ['jest-junit', { outputDirectory: 'test-results' }]],
  coverageReporters: ['cobertura', 'text-summary', 'html'],
  collectCoverageFrom: [
    '<rootDir>/src/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/*test-utils*',
  ],
  coverageDirectory: 'coverage',

  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },

  transformIgnorePatterns: ['node_modules/(?!(.*\\.mjs$))'],
  testTimeout: 10000,

  // Helps debugging by detecting open handles
  detectOpenHandles: process.env.CI ? false : true,
  // Speeds up CI by forcing exit
  forceExit: process.env.CI ? true : false,
};

export default createJestConfig(config);
