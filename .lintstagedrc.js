// required for 'next lint' command to work with lint-staged
// https://nextjs.org/docs/pages/api-reference/config/eslint#running-lint-on-staged-files

// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require('path')

const buildEslintCommand = (filenames) =>
    `next lint --fix --file ${filenames
        .map((f) => path.relative(process.cwd(), f))
        .join(' --file ')}`

module.exports = {
    '*.{js,jsx,ts,tsx}': [buildEslintCommand],
}
